'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Sidebar from '@/components/Sidebar'

interface DashboardLayoutProps {
  children: React.ReactNode
  allowedRoles: string[]
}

export default function DashboardLayout({ children, allowedRoles }: DashboardLayoutProps) {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const token = localStorage.getItem('token')
    const userData = localStorage.getItem('user')

    if (!token || !userData) {
      router.push('/auth/login')
      return
    }

    try {
      const parsedUser = JSON.parse(userData)

      // Check if user role is allowed
      if (!allowedRoles.includes(parsedUser.role)) {
        // Redirect to appropriate dashboard based on role
        switch (parsedUser.role) {
          case 'admin':
            router.push('/admin/dashboard')
            break
          case 'super_admin':
            router.push('/superadmin/dashboard')
            break
          case 'company':
            router.push('/company/dashboard')
            break
          case 'individual':
            router.push('/user/dashboard')
            break
          case 'government':
            router.push('/government/dashboard')
            break
          default:
            router.push('/auth/login')
        }
        return
      }

      // Check if account is approved (except for admins)
      if (parsedUser.role !== 'admin' && parsedUser.role !== 'super_admin' && parsedUser.status !== 'approved') {
        router.push('/account-status')
        return
      }

      setUser(parsedUser)
      setLoading(false)
    } catch (error) {
      console.error('DashboardLayout - Error parsing user data:', error)
      router.push('/auth/login')
      return
    }
  }, [router, allowedRoles])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar userRole={user.role} />
      <main className="flex-1 overflow-auto">
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  )
}
