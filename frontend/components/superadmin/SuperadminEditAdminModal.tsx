'use client'

import { useState, useEffect } from 'react'
import { X, Edit, Shield, Mail, Phone, User, Building } from 'lucide-react'
import { superAdminService } from '@/services/superadmin'

interface AdminUser {
  id: string
  email: string
  role: 'admin' | 'super_admin'
  status: 'approved' | 'suspended' | 'blocked' | 'pending' | 'documents_submitted' | 'under_review'
  profile: {
    fullName: string
    phone?: string
    department?: string
  }
  permissions: string[]
  createdAt: string
  lastLogin?: string
  createdBy?: string
}

interface SuperadminEditAdminModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  admin: AdminUser
}

interface AdminFormData {
  email: string
  fullName: string
  role: 'admin' | 'super_admin'
  status: 'approved' | 'suspended' | 'blocked' | 'pending'
  department: string
  phone: string
}

export default function SuperadminEditAdminModal({ isOpen, onClose, onSuccess, admin }: SuperadminEditAdminModalProps) {
  const [formData, setFormData] = useState<AdminFormData>({
    email: '',
    fullName: '',
    role: 'admin',
    status: 'approved',
    department: '',
    phone: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Initialize form data when admin prop changes
  useEffect(() => {
    if (admin) {
      setFormData({
        email: admin.email,
        fullName: admin.profile?.fullName || '',
        role: admin.role,
        status: admin.status as 'approved' | 'suspended' | 'blocked' | 'pending',
        department: admin.profile?.department || '',
        phone: admin.profile?.phone || ''
      })
    }
  }, [admin])

  const handleInputChange = (field: keyof AdminFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.email || !formData.fullName) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    try {
      setLoading(true)
      await superAdminService.updateAdmin(admin.id, {
        email: formData.email,
        role: formData.role,
        status: formData.status,
        profile: {
          fullName: formData.fullName,
          department: formData.department || undefined,
          phone: formData.phone || undefined
        }
      })
      
      onSuccess()
    } catch (error: any) {
      setError(error.message || 'حدث خطأ في تحديث المدير')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Edit className="h-6 w-6 text-green-600 ml-3" />
            <h2 className="text-xl font-bold text-gray-900">تعديل المدير</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Full Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="h-4 w-4 inline ml-1" />
              الاسم الكامل *
            </label>
            <input
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="أدخل الاسم الكامل"
              required
            />
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="h-4 w-4 inline ml-1" />
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="أدخل البريد الإلكتروني"
              required
            />
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Phone className="h-4 w-4 inline ml-1" />
              رقم الهاتف
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="أدخل رقم الهاتف"
            />
          </div>

          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Building className="h-4 w-4 inline ml-1" />
              القسم
            </label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => handleInputChange('department', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="أدخل القسم"
            />
          </div>

          {/* Role */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Shield className="h-4 w-4 inline ml-1" />
              الدور *
            </label>
            <select
              value={formData.role}
              onChange={(e) => handleInputChange('role', e.target.value as 'admin' | 'super_admin')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="admin">مدير</option>
              <option value="super_admin">مدير عام</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة *
            </label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value as 'approved' | 'suspended' | 'blocked' | 'pending')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="approved">نشط</option>
              <option value="suspended">معلق</option>
              <option value="blocked">محظور</option>
              <option value="pending">في الانتظار</option>
            </select>
          </div>

          {/* Buttons */}
          <div className="flex items-center justify-end space-x-3 space-x-reverse pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              disabled={loading}
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري التحديث...
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 ml-2" />
                  تحديث المدير
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
