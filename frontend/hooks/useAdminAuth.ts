'use client'

import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'

interface UseAdminAuthOptions {
  requiredRole?: 'admin' | 'super_admin'
  redirectTo?: string
}

export function useAdminAuth(options: UseAdminAuthOptions = {}) {
  const { user } = useAuth()
  const router = useRouter()
  const { requiredRole = 'admin', redirectTo } = options

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/auth/login')
      return
    }

    // Check role-based access
    if (requiredRole === 'super_admin' && user.role !== 'super_admin') {
      // Redirect non-super_admin users to appropriate dashboard
      if (user.role === 'admin') {
        router.push(redirectTo || '/admin/dashboard')
      } else {
        router.push(redirectTo || '/auth/login')
      }
      return
    }

    // Admin pages should only be accessible by admin role (not super_admin)
    if (requiredRole === 'admin' && user.role !== 'admin') {
      if (user.role === 'super_admin') {
        router.push('/superadmin/dashboard')
      } else {
        router.push(redirectTo || '/auth/login')
      }
      return
    }
  }, [user, requiredRole, redirectTo, router])

  return {
    user,
    isAuthenticated: !!user,
    isAuthorized: user && (
      requiredRole === 'super_admin' ? user.role === 'super_admin' :
      requiredRole === 'admin' ? user.role === 'admin' :
      true
    ),
    loading: !user
  }
}
