"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/admin-management/page",{

/***/ "(app-pages-browser)/./app/superadmin/admin-management/page.tsx":
/*!**************************************************!*\
  !*** ./app/superadmin/admin-management/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SuperAdminManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAdminAuth */ \"(app-pages-browser)/./hooks/useAdminAuth.ts\");\n/* harmony import */ var _components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/superadmin/SuperadminCreateAdminModal */ \"(app-pages-browser)/./components/superadmin/SuperadminCreateAdminModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SuperAdminManagementPage() {\n    var _selectedAdmin_profile, _selectedAdmin_profile1, _selectedAdmin_profile2;\n    _s();\n    const { user, isAuthorized } = (0,_hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth)({\n        requiredRole: \"super_admin\"\n    });\n    const [admins, setAdmins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingAdmin, setEditingAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Don't render if user is not authorized\n    if (!isAuthorized) {\n        return null;\n    }\n    const [selectedAdmin, setSelectedAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterRole, setFilterRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only fetch once when user is authorized and we haven't initialized yet\n        if (isAuthorized && !hasInitialized) {\n            setHasInitialized(true);\n            fetchAdmins();\n        }\n    }, [\n        isAuthorized,\n        hasInitialized\n    ]) // Only depend on authorization and initialization state\n    ;\n    // Separate useEffect for filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hasInitialized) {\n            fetchAdmins();\n        }\n    }, [\n        filterRole,\n        filterStatus,\n        searchTerm\n    ]) // Fetch when filters change\n    ;\n    const fetchAdmins = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real admin data from backend API\n            const token = localStorage.getItem(\"token\");\n            const params = new URLSearchParams({\n                page: \"1\",\n                limit: \"50\",\n                role: filterRole !== \"all\" ? filterRole : \"\",\n                status: filterStatus !== \"all\" ? filterStatus : \"\",\n                search: searchTerm\n            });\n            console.log(\"Making API call to:\", \"http://localhost:5000/api/superadmin/admins?\".concat(params));\n            console.log(\"Token:\", token ? \"Present\" : \"Missing\");\n            const response = await fetch(\"http://localhost:5000/api/superadmin/admins?\".concat(params), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log(\"Response status:\", response.status, response.statusText);\n            console.log(\"Response.ok:\", response.ok);\n            if (response.ok) {\n                var _result_data, _result_data1, _result_data2, _result_data3, _adminsData_;\n                console.log(\"✅ API call successful, processing response...\");\n                const result = await response.json();\n                console.log(\"API Response:\", result);\n                console.log(\"Admins data:\", (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.admins);\n                console.log(\"Stats data:\", (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.stats);\n                const adminsData = ((_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : _result_data2.admins) || [];\n                const statsData = ((_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.stats) || {\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                };\n                console.log(\"Setting admins data:\", adminsData.length, \"admins\");\n                console.log(\"First admin sample:\", adminsData[0]);\n                console.log(\"First admin permissions:\", (_adminsData_ = adminsData[0]) === null || _adminsData_ === void 0 ? void 0 : _adminsData_.permissions);\n                console.log(\"Setting stats data:\", statsData);\n                setAdmins(adminsData);\n                setStats(statsData);\n            } else {\n                // API call failed - show empty data instead of fake data\n                console.error(\"❌ API call failed\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n                setAdmins([]);\n                setStats({\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching admins:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stats are now fetched together with admins\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, this);\n            case \"blocked\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n            case \"documents_submitted\":\n            case \"under_review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleIcon = (role)=>{\n        return role === \"super_admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-4 w-4 text-purple-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const filteredAdmins = admins.filter((admin)=>{\n        var _admin_profile;\n        const matchesSearch = searchTerm === \"\" || admin.email.toLowerCase().includes(searchTerm.toLowerCase()) || (((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : _admin_profile.fullName) || \"\").toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesRole = filterRole === \"all\" || admin.role === filterRole;\n        const matchesStatus = filterStatus === \"all\" || admin.status === filterStatus;\n        return matchesSearch && matchesRole && matchesStatus;\n    });\n    // Show loading screen while fetching data\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل المدراء...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إدارة المدراء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة حسابات المدراء والصلاحيات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowCreateModal(true),\n                            className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة مدير جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"إجمالي المدراء\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stats.totalAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء النشطون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: stats.activeAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء المعلقون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: stats.suspendedAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء العامون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: stats.superAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"البحث\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"البحث بالاسم أو البريد الإلكتروني...\",\n                                            className: \"w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الدور\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterRole,\n                                    onChange: (e)=>setFilterRole(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الأدوار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"super_admin\",\n                                            children: \"مدير عام\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"admin\",\n                                            children: \"مدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الحالة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterStatus,\n                                    onChange: (e)=>setFilterStatus(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الحالات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"approved\",\n                                            children: \"نشط\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"suspended\",\n                                            children: \"معلق\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"blocked\",\n                                            children: \"محظور\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"pending\",\n                                            children: \"في الانتظار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: [\n                                \"قائمة المدراء (\",\n                                filteredAdmins.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الصلاحيات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"إجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredAdmins.map((admin)=>{\n                                            var _admin_profile_fullName, _admin_profile, _admin_email, _admin_profile1, _admin_profile2, _admin_profile3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 h-10 w-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: ((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : (_admin_profile_fullName = _admin_profile.fullName) === null || _admin_profile_fullName === void 0 ? void 0 : _admin_profile_fullName.charAt(0)) || ((_admin_email = admin.email) === null || _admin_email === void 0 ? void 0 : _admin_email.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mr-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_admin_profile1 = admin.profile) === null || _admin_profile1 === void 0 ? void 0 : _admin_profile1.fullName) || admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_admin_profile2 = admin.profile) === null || _admin_profile2 === void 0 ? void 0 : _admin_profile2.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 flex items-center mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"h-3 w-3 ml-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                                    lineNumber: 384,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                admin.profile.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getRoleIcon(admin.role),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm font-medium \".concat(admin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                                    children: admin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getStatusIcon(admin.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm \".concat(admin.status === \"approved\" ? \"text-green-600\" : admin.status === \"suspended\" ? \"text-red-600\" : admin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                                    children: admin.status === \"approved\" ? \"نشط\" : admin.status === \"suspended\" ? \"معلق\" : admin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: ((_admin_profile3 = admin.profile) === null || _admin_profile3 === void 0 ? void 0 : _admin_profile3.department) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: formatTimestamp(admin.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: admin.lastLogin ? formatTimestamp(admin.lastLogin) : \"لم يسجل دخول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: [\n                                                                admin.permissions.slice(0, 2).map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: permission.replace(\"_\", \" \")\n                                                                    }, permission, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                admin.permissions.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        admin.permissions.length - 2\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAdmin(admin),\n                                                                    className: \"text-blue-600 hover:text-blue-800\",\n                                                                    title: \"عرض التفاصيل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"تعديل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                admin.status === \"approved\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-yellow-600 hover:text-yellow-800\",\n                                                                    title: \"تعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"إلغاء التعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                admin.role !== \"super_admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-red-600 hover:text-red-800\",\n                                                                    title: \"حذف\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, admin.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            filteredAdmins.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"لا توجد مدراء تطابق المعايير المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, this),\n            showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showCreateModal,\n                onClose: ()=>setShowCreateModal(false),\n                onSuccess: ()=>{\n                    setShowCreateModal(false);\n                    fetchAdmins() // Refresh the admin list\n                    ;\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 498,\n                columnNumber: 9\n            }, this),\n            selectedAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"تفاصيل المدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedAdmin(null),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الاسم الكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile = selectedAdmin.profile) === null || _selectedAdmin_profile === void 0 ? void 0 : _selectedAdmin_profile.fullName) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"البريد الإلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.email\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile1 = selectedAdmin.profile) === null || _selectedAdmin_profile1 === void 0 ? void 0 : _selectedAdmin_profile1.phone) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile2 = selectedAdmin.profile) === null || _selectedAdmin_profile2 === void 0 ? void 0 : _selectedAdmin_profile2.department) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getRoleIcon(selectedAdmin.role),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                            children: selectedAdmin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getStatusIcon(selectedAdmin.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.status === \"approved\" ? \"text-green-600\" : selectedAdmin.status === \"suspended\" ? \"text-red-600\" : selectedAdmin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                            children: selectedAdmin.status === \"approved\" ? \"نشط\" : selectedAdmin.status === \"suspended\" ? \"معلق\" : selectedAdmin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: formatTimestamp(selectedAdmin.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.lastLogin ? formatTimestamp(selectedAdmin.lastLogin) : \"لم يسجل دخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                            children: \"الصلاحيات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                selectedAdmin.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                                        children: permission.replace(\"_\", \" \")\n                                                    }, permission, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                selectedAdmin.permissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"لا توجد صلاحيات محددة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this),\n                                selectedAdmin.createdBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"تم الإنشاء بواسطة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: selectedAdmin.createdBy\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 640,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end p-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedAdmin(null),\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"إغلاق\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 510,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(SuperAdminManagementPage, \"Y1UWIsYbOAMElQifGBZue+4RTZ4=\", false, function() {\n    return [\n        _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth\n    ];\n});\n_c = SuperAdminManagementPage;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/superadmin/admin-management/page.tsx\n"));

/***/ })

});