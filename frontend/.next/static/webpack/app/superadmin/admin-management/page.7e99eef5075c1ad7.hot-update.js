"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/admin-management/page",{

/***/ "(app-pages-browser)/./app/superadmin/admin-management/page.tsx":
/*!**************************************************!*\
  !*** ./app/superadmin/admin-management/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SuperAdminManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAdminAuth */ \"(app-pages-browser)/./hooks/useAdminAuth.ts\");\n/* harmony import */ var _components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/superadmin/SuperadminCreateAdminModal */ \"(app-pages-browser)/./components/superadmin/SuperadminCreateAdminModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,User,UserPlus,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SuperAdminManagementPage() {\n    var _selectedAdmin_profile, _selectedAdmin_profile1, _selectedAdmin_profile2;\n    _s();\n    const { user, isAuthorized } = (0,_hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth)({\n        requiredRole: \"super_admin\"\n    });\n    const [admins, setAdmins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingAdmin, setEditingAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Don't render if user is not authorized\n    if (!isAuthorized) {\n        return null;\n    }\n    const [selectedAdmin, setSelectedAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterRole, setFilterRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only fetch once when user is authorized and we haven't initialized yet\n        if (isAuthorized && !hasInitialized) {\n            setHasInitialized(true);\n            fetchAdmins();\n        }\n    }, [\n        isAuthorized,\n        hasInitialized\n    ]) // Only depend on authorization and initialization state\n    ;\n    // Separate useEffect for filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hasInitialized) {\n            fetchAdmins();\n        }\n    }, [\n        filterRole,\n        filterStatus,\n        searchTerm\n    ]) // Fetch when filters change\n    ;\n    const fetchAdmins = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real admin data from backend API\n            const token = localStorage.getItem(\"token\");\n            const params = new URLSearchParams({\n                page: \"1\",\n                limit: \"50\",\n                role: filterRole !== \"all\" ? filterRole : \"\",\n                status: filterStatus !== \"all\" ? filterStatus : \"\",\n                search: searchTerm\n            });\n            console.log(\"Making API call to:\", \"http://localhost:5000/api/superadmin/admins?\".concat(params));\n            console.log(\"Token:\", token ? \"Present\" : \"Missing\");\n            const response = await fetch(\"http://localhost:5000/api/superadmin/admins?\".concat(params), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log(\"Response status:\", response.status, response.statusText);\n            console.log(\"Response.ok:\", response.ok);\n            if (response.ok) {\n                var _result_data, _result_data1, _result_data2, _result_data3, _adminsData_;\n                console.log(\"✅ API call successful, processing response...\");\n                const result = await response.json();\n                console.log(\"API Response:\", result);\n                console.log(\"Admins data:\", (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.admins);\n                console.log(\"Stats data:\", (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.stats);\n                const adminsData = ((_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : _result_data2.admins) || [];\n                const statsData = ((_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.stats) || {\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                };\n                console.log(\"Setting admins data:\", adminsData.length, \"admins\");\n                console.log(\"First admin sample:\", adminsData[0]);\n                console.log(\"First admin permissions:\", (_adminsData_ = adminsData[0]) === null || _adminsData_ === void 0 ? void 0 : _adminsData_.permissions);\n                console.log(\"Setting stats data:\", statsData);\n                setAdmins(adminsData);\n                setStats(statsData);\n            } else {\n                // API call failed - show empty data instead of fake data\n                console.error(\"❌ API call failed\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n                setAdmins([]);\n                setStats({\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching admins:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stats are now fetched together with admins\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, this);\n            case \"blocked\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n            case \"documents_submitted\":\n            case \"under_review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleIcon = (role)=>{\n        return role === \"super_admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-4 w-4 text-purple-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const filteredAdmins = admins.filter((admin)=>{\n        var _admin_profile;\n        const matchesSearch = searchTerm === \"\" || admin.email.toLowerCase().includes(searchTerm.toLowerCase()) || (((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : _admin_profile.fullName) || \"\").toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesRole = filterRole === \"all\" || admin.role === filterRole;\n        const matchesStatus = filterStatus === \"all\" || admin.status === filterStatus;\n        return matchesSearch && matchesRole && matchesStatus;\n    });\n    // Show loading screen while fetching data\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل المدراء...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إدارة المدراء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة حسابات المدراء والصلاحيات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowCreateModal(true),\n                            className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة مدير جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"إجمالي المدراء\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stats.totalAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء النشطون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: stats.activeAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء المعلقون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: stats.suspendedAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء العامون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: stats.superAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"البحث\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"البحث بالاسم أو البريد الإلكتروني...\",\n                                            className: \"w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الدور\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterRole,\n                                    onChange: (e)=>setFilterRole(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الأدوار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"super_admin\",\n                                            children: \"مدير عام\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"admin\",\n                                            children: \"مدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الحالة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterStatus,\n                                    onChange: (e)=>setFilterStatus(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الحالات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"approved\",\n                                            children: \"نشط\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"suspended\",\n                                            children: \"معلق\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"blocked\",\n                                            children: \"محظور\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"pending\",\n                                            children: \"في الانتظار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: [\n                                \"قائمة المدراء (\",\n                                filteredAdmins.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الصلاحيات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"إجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredAdmins.map((admin)=>{\n                                            var _admin_profile_fullName, _admin_profile, _admin_email, _admin_profile1, _admin_profile2, _admin_profile3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 h-10 w-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: ((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : (_admin_profile_fullName = _admin_profile.fullName) === null || _admin_profile_fullName === void 0 ? void 0 : _admin_profile_fullName.charAt(0)) || ((_admin_email = admin.email) === null || _admin_email === void 0 ? void 0 : _admin_email.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mr-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_admin_profile1 = admin.profile) === null || _admin_profile1 === void 0 ? void 0 : _admin_profile1.fullName) || admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_admin_profile2 = admin.profile) === null || _admin_profile2 === void 0 ? void 0 : _admin_profile2.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 flex items-center mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"h-3 w-3 ml-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                                    lineNumber: 384,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                admin.profile.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getRoleIcon(admin.role),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm font-medium \".concat(admin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                                    children: admin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getStatusIcon(admin.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm \".concat(admin.status === \"approved\" ? \"text-green-600\" : admin.status === \"suspended\" ? \"text-red-600\" : admin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                                    children: admin.status === \"approved\" ? \"نشط\" : admin.status === \"suspended\" ? \"معلق\" : admin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: ((_admin_profile3 = admin.profile) === null || _admin_profile3 === void 0 ? void 0 : _admin_profile3.department) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: formatTimestamp(admin.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: admin.lastLogin ? formatTimestamp(admin.lastLogin) : \"لم يسجل دخول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: [\n                                                                admin.permissions.slice(0, 2).map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: permission.replace(\"_\", \" \")\n                                                                    }, permission, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                admin.permissions.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        admin.permissions.length - 2\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAdmin(admin),\n                                                                    className: \"text-blue-600 hover:text-blue-800\",\n                                                                    title: \"عرض التفاصيل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        setEditingAdmin(admin);\n                                                                        setShowEditModal(true);\n                                                                    },\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"تعديل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                admin.status === \"approved\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-yellow-600 hover:text-yellow-800\",\n                                                                    title: \"تعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"إلغاء التعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                admin.role !== \"super_admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-red-600 hover:text-red-800\",\n                                                                    title: \"حذف\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, admin.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            filteredAdmins.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"لا توجد مدراء تطابق المعايير المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, this),\n            showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showCreateModal,\n                onClose: ()=>setShowCreateModal(false),\n                onSuccess: ()=>{\n                    setShowCreateModal(false);\n                    fetchAdmins() // Refresh the admin list\n                    ;\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this),\n            selectedAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"تفاصيل المدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedAdmin(null),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الاسم الكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile = selectedAdmin.profile) === null || _selectedAdmin_profile === void 0 ? void 0 : _selectedAdmin_profile.fullName) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"البريد الإلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.email\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile1 = selectedAdmin.profile) === null || _selectedAdmin_profile1 === void 0 ? void 0 : _selectedAdmin_profile1.phone) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile2 = selectedAdmin.profile) === null || _selectedAdmin_profile2 === void 0 ? void 0 : _selectedAdmin_profile2.department) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getRoleIcon(selectedAdmin.role),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                            children: selectedAdmin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getStatusIcon(selectedAdmin.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.status === \"approved\" ? \"text-green-600\" : selectedAdmin.status === \"suspended\" ? \"text-red-600\" : selectedAdmin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                            children: selectedAdmin.status === \"approved\" ? \"نشط\" : selectedAdmin.status === \"suspended\" ? \"معلق\" : selectedAdmin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: formatTimestamp(selectedAdmin.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.lastLogin ? formatTimestamp(selectedAdmin.lastLogin) : \"لم يسجل دخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                            children: \"الصلاحيات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                selectedAdmin.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                                        children: permission.replace(\"_\", \" \")\n                                                    }, permission, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                selectedAdmin.permissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"لا توجد صلاحيات محددة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this),\n                                selectedAdmin.createdBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"تم الإنشاء بواسطة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_User_UserPlus_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: selectedAdmin.createdBy\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end p-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedAdmin(null),\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"إغلاق\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 514,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(SuperAdminManagementPage, \"Y1UWIsYbOAMElQifGBZue+4RTZ4=\", false, function() {\n    return [\n        _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth\n    ];\n});\n_c = SuperAdminManagementPage;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdXBlcmFkbWluL2FkbWluLW1hbmFnZW1lbnQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDUTtBQUN3QztBQXVCdEU7QUF5Qk4sU0FBU3NCO1FBcWU2QkMsd0JBZ0JBQSx5QkFRQUE7O0lBNWZuRCxNQUFNLEVBQUVDLElBQUksRUFBRUMsWUFBWSxFQUFFLEdBQUd2QixpRUFBWUEsQ0FBQztRQUFFd0IsY0FBYztJQUFjO0lBQzFFLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHNUIsK0NBQVFBLENBQWMsRUFBRTtJQUNwRCxNQUFNLENBQUM2QixPQUFPQyxTQUFTLEdBQUc5QiwrQ0FBUUEsQ0FBb0I7SUFDdEQsTUFBTSxDQUFDK0IsU0FBU0MsV0FBVyxHQUFHaEMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDaUMsaUJBQWlCQyxtQkFBbUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ21DLGVBQWVDLGlCQUFpQixHQUFHcEMsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDcUMsY0FBY0MsZ0JBQWdCLEdBQUd0QywrQ0FBUUEsQ0FBbUI7SUFFbkUseUNBQXlDO0lBQ3pDLElBQUksQ0FBQ3lCLGNBQWM7UUFDakIsT0FBTztJQUNUO0lBQ0EsTUFBTSxDQUFDRixlQUFlZ0IsaUJBQWlCLEdBQUd2QywrQ0FBUUEsQ0FBbUI7SUFDckUsTUFBTSxDQUFDd0MsWUFBWUMsY0FBYyxHQUFHekMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMEMsWUFBWUMsY0FBYyxHQUFHM0MsK0NBQVFBLENBQVM7SUFDckQsTUFBTSxDQUFDNEMsY0FBY0MsZ0JBQWdCLEdBQUc3QywrQ0FBUUEsQ0FBUztJQUN6RCxNQUFNLENBQUM4QyxnQkFBZ0JDLGtCQUFrQixHQUFHL0MsK0NBQVFBLENBQUM7SUFFckRDLGdEQUFTQSxDQUFDO1FBQ1IseUVBQXlFO1FBQ3pFLElBQUl3QixnQkFBZ0IsQ0FBQ3FCLGdCQUFnQjtZQUNuQ0Msa0JBQWtCO1lBQ2xCQztRQUNGO0lBQ0YsR0FBRztRQUFDdkI7UUFBY3FCO0tBQWUsRUFBRSx3REFBd0Q7O0lBRTNGLHdDQUF3QztJQUN4QzdDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTZDLGdCQUFnQjtZQUNsQkU7UUFDRjtJQUNGLEdBQUc7UUFBQ047UUFBWUU7UUFBY0o7S0FBVyxFQUFFLDRCQUE0Qjs7SUFFdkUsTUFBTVEsY0FBYztRQUNsQixJQUFJO1lBQ0ZoQixXQUFXO1lBRVgseUNBQXlDO1lBQ3pDLE1BQU1pQixRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsTUFBTUMsU0FBUyxJQUFJQyxnQkFBZ0I7Z0JBQ2pDQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxNQUFNZCxlQUFlLFFBQVFBLGFBQWE7Z0JBQzFDZSxRQUFRYixpQkFBaUIsUUFBUUEsZUFBZTtnQkFDaERjLFFBQVFsQjtZQUNWO1lBRUFtQixRQUFRQyxHQUFHLENBQUMsdUJBQXVCLCtDQUFzRCxPQUFQUjtZQUNsRk8sUUFBUUMsR0FBRyxDQUFDLFVBQVVYLFFBQVEsWUFBWTtZQUUxQyxNQUFNWSxXQUFXLE1BQU1DLE1BQU0sK0NBQXNELE9BQVBWLFNBQVU7Z0JBQ3BGVyxTQUFTO29CQUNQLGlCQUFpQixVQUFnQixPQUFOZDtvQkFDM0IsZ0JBQWdCO2dCQUNsQjtZQUNGO1lBRUFVLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JDLFNBQVNKLE1BQU0sRUFBRUksU0FBU0csVUFBVTtZQUNwRUwsUUFBUUMsR0FBRyxDQUFDLGdCQUFnQkMsU0FBU0ksRUFBRTtZQUV2QyxJQUFJSixTQUFTSSxFQUFFLEVBQUU7b0JBSWFDLGNBQ0RBLGVBRVJBLGVBQ0RBLGVBU3NCQztnQkFoQnhDUixRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTU0sU0FBUyxNQUFNTCxTQUFTTyxJQUFJO2dCQUNsQ1QsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQk07Z0JBQzdCUCxRQUFRQyxHQUFHLENBQUMsaUJBQWdCTSxlQUFBQSxPQUFPRyxJQUFJLGNBQVhILG1DQUFBQSxhQUFhdkMsTUFBTTtnQkFDL0NnQyxRQUFRQyxHQUFHLENBQUMsZ0JBQWVNLGdCQUFBQSxPQUFPRyxJQUFJLGNBQVhILG9DQUFBQSxjQUFhckMsS0FBSztnQkFFN0MsTUFBTXNDLGFBQWFELEVBQUFBLGdCQUFBQSxPQUFPRyxJQUFJLGNBQVhILG9DQUFBQSxjQUFhdkMsTUFBTSxLQUFJLEVBQUU7Z0JBQzVDLE1BQU0yQyxZQUFZSixFQUFBQSxnQkFBQUEsT0FBT0csSUFBSSxjQUFYSCxvQ0FBQUEsY0FBYXJDLEtBQUssS0FBSTtvQkFDdEMwQyxhQUFhO29CQUNiQyxjQUFjO29CQUNkQyxpQkFBaUI7b0JBQ2pCQyxhQUFhO2dCQUNmO2dCQUVBZixRQUFRQyxHQUFHLENBQUMsd0JBQXdCTyxXQUFXUSxNQUFNLEVBQUU7Z0JBQ3ZEaEIsUUFBUUMsR0FBRyxDQUFDLHVCQUF1Qk8sVUFBVSxDQUFDLEVBQUU7Z0JBQ2hEUixRQUFRQyxHQUFHLENBQUMsNkJBQTRCTyxlQUFBQSxVQUFVLENBQUMsRUFBRSxjQUFiQSxtQ0FBQUEsYUFBZVMsV0FBVztnQkFDbEVqQixRQUFRQyxHQUFHLENBQUMsdUJBQXVCVTtnQkFFbkMxQyxVQUFVdUM7Z0JBQ1ZyQyxTQUFTd0M7WUFDWCxPQUFPO2dCQUNMLHlEQUF5RDtnQkFDekRYLFFBQVFrQixLQUFLLENBQUMscUJBQXFCaEIsU0FBU0osTUFBTSxFQUFFSSxTQUFTRyxVQUFVO2dCQUN2RSxNQUFNYyxZQUFZLE1BQU1qQixTQUFTa0IsSUFBSTtnQkFDckNwQixRQUFRa0IsS0FBSyxDQUFDLG1CQUFtQkM7Z0JBRWpDbEQsVUFBVSxFQUFFO2dCQUNaRSxTQUFTO29CQUNQeUMsYUFBYTtvQkFDYkMsY0FBYztvQkFDZEMsaUJBQWlCO29CQUNqQkMsYUFBYTtnQkFDZjtZQUNGO1FBQ0YsRUFBRSxPQUFPRyxPQUFPO1lBQ2RsQixRQUFRa0IsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUMsU0FBVTtZQUNSN0MsV0FBVztRQUNiO0lBQ0Y7SUFFQSw2Q0FBNkM7SUFFN0MsTUFBTWdELGdCQUFnQixDQUFDdkI7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDMUMsME1BQVdBO29CQUFDa0UsV0FBVTs7Ozs7O1lBQ2hDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNqRSwwTUFBT0E7b0JBQUNpRSxXQUFVOzs7Ozs7WUFDNUIsS0FBSztnQkFDSCxxQkFBTyw4REFBQ2pFLDBNQUFPQTtvQkFBQ2lFLFdBQVU7Ozs7OztZQUM1QixLQUFLO1lBQ0wsS0FBSztZQUNMLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNoRSwwTUFBYUE7b0JBQUNnRSxXQUFVOzs7Ozs7WUFDbEM7Z0JBQ0UscUJBQU8sOERBQUNoRSwwTUFBYUE7b0JBQUNnRSxXQUFVOzs7Ozs7UUFDcEM7SUFDRjtJQUVBLE1BQU1DLGNBQWMsQ0FBQzFCO1FBQ25CLE9BQU9BLFNBQVMsOEJBQ2QsOERBQUM3QywwTUFBS0E7WUFBQ3NFLFdBQVU7Ozs7O2lDQUNqQiw4REFBQzdFLDBNQUFNQTtZQUFDNkUsV0FBVTs7Ozs7O0lBQ3RCO0lBRUEsTUFBTUUsa0JBQWtCLENBQUNDO1FBQ3ZCLE9BQU8sSUFBSUMsS0FBS0QsV0FBV0UsY0FBYyxDQUFDLFNBQVM7WUFDakRDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxLQUFLO1lBQ0xDLE1BQU07WUFDTkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUJqRSxPQUFPa0UsTUFBTSxDQUFDQyxDQUFBQTtZQUdoQ0E7UUFGSCxNQUFNQyxnQkFBZ0J2RCxlQUFlLE1BQ25Dc0QsTUFBTUUsS0FBSyxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFELFdBQVd5RCxXQUFXLE9BQ3pELENBQUNILEVBQUFBLGlCQUFBQSxNQUFNSyxPQUFPLGNBQWJMLHFDQUFBQSxlQUFlTSxRQUFRLEtBQUksRUFBQyxFQUFHSCxXQUFXLEdBQUdDLFFBQVEsQ0FBQzFELFdBQVd5RCxXQUFXO1FBQy9FLE1BQU1JLGNBQWMzRCxlQUFlLFNBQVNvRCxNQUFNdEMsSUFBSSxLQUFLZDtRQUMzRCxNQUFNNEQsZ0JBQWdCMUQsaUJBQWlCLFNBQVNrRCxNQUFNckMsTUFBTSxLQUFLYjtRQUVqRSxPQUFPbUQsaUJBQWlCTSxlQUFlQztJQUN6QztJQUVBLDBDQUEwQztJQUMxQyxJQUFJdkUsU0FBUztRQUNYLHFCQUNFLDhEQUFDd0U7WUFBSXRCLFdBQVU7c0JBQ2IsNEVBQUNzQjtnQkFBSXRCLFdBQVU7O2tDQUNiLDhEQUFDc0I7d0JBQUl0QixXQUFVOzs7Ozs7a0NBQ2YsOERBQUN1Qjt3QkFBRXZCLFdBQVU7a0NBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUkxQztJQUVBLHFCQUNFLDhEQUFDc0I7UUFBSXRCLFdBQVU7OzBCQUViLDhEQUFDc0I7Z0JBQUl0QixXQUFVOzBCQUNiLDRFQUFDc0I7b0JBQUl0QixXQUFVOztzQ0FDYiw4REFBQ3NCOzs4Q0FDQyw4REFBQ0U7b0NBQUd4QixXQUFVOztzREFDWiw4REFBQzdFLDBNQUFNQTs0Q0FBQzZFLFdBQVU7Ozs7Ozt3Q0FBK0I7Ozs7Ozs7OENBR25ELDhEQUFDdUI7b0NBQUV2QixXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7O3NDQUdwQyw4REFBQ3lCOzRCQUNDQyxTQUFTLElBQU16RSxtQkFBbUI7NEJBQ2xDK0MsV0FBVTs7OENBRVYsOERBQUM1RSwwTUFBUUE7b0NBQUM0RSxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPMUNwRCx1QkFDQyw4REFBQzBFO2dCQUFJdEIsV0FBVTs7a0NBQ2IsOERBQUNzQjt3QkFBSXRCLFdBQVU7a0NBQ2IsNEVBQUNzQjs0QkFBSXRCLFdBQVU7OzhDQUNiLDhEQUFDc0I7O3NEQUNDLDhEQUFDQzs0Q0FBRXZCLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDdUI7NENBQUV2QixXQUFVO3NEQUFvQ3BELE1BQU0wQyxXQUFXOzs7Ozs7Ozs7Ozs7OENBRXBFLDhEQUFDbkUsME1BQU1BO29DQUFDNkUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXRCLDhEQUFDc0I7d0JBQUl0QixXQUFVO2tDQUNiLDRFQUFDc0I7NEJBQUl0QixXQUFVOzs4Q0FDYiw4REFBQ3NCOztzREFDQyw4REFBQ0M7NENBQUV2QixXQUFVO3NEQUF3Qjs7Ozs7O3NEQUNyQyw4REFBQ3VCOzRDQUFFdkIsV0FBVTtzREFBcUNwRCxNQUFNMkMsWUFBWTs7Ozs7Ozs7Ozs7OzhDQUV0RSw4REFBQ3pELDBNQUFXQTtvQ0FBQ2tFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUkzQiw4REFBQ3NCO3dCQUFJdEIsV0FBVTtrQ0FDYiw0RUFBQ3NCOzRCQUFJdEIsV0FBVTs7OENBQ2IsOERBQUNzQjs7c0RBQ0MsOERBQUNDOzRDQUFFdkIsV0FBVTtzREFBd0I7Ozs7OztzREFDckMsOERBQUN1Qjs0Q0FBRXZCLFdBQVU7c0RBQW1DcEQsTUFBTTRDLGVBQWU7Ozs7Ozs7Ozs7Ozs4Q0FFdkUsOERBQUN6RCwwTUFBT0E7b0NBQUNpRSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJdkIsOERBQUNzQjt3QkFBSXRCLFdBQVU7a0NBQ2IsNEVBQUNzQjs0QkFBSXRCLFdBQVU7OzhDQUNiLDhEQUFDc0I7O3NEQUNDLDhEQUFDQzs0Q0FBRXZCLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDdUI7NENBQUV2QixXQUFVO3NEQUFzQ3BELE1BQU02QyxXQUFXOzs7Ozs7Ozs7Ozs7OENBRXRFLDhEQUFDL0QsME1BQUtBO29DQUFDc0UsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3pCLDhEQUFDc0I7Z0JBQUl0QixXQUFVOzBCQUNiLDRFQUFDc0I7b0JBQUl0QixXQUFVOztzQ0FDYiw4REFBQ3NCOzs4Q0FDQyw4REFBQ0s7b0NBQU0zQixXQUFVOzhDQUErQzs7Ozs7OzhDQUNoRSw4REFBQ3NCO29DQUFJdEIsV0FBVTs7c0RBQ2IsOERBQUMvRCwyTUFBTUE7NENBQUMrRCxXQUFVOzs7Ozs7c0RBQ2xCLDhEQUFDNEI7NENBQ0NDLE1BQUs7NENBQ0xDLE9BQU92RTs0Q0FDUHdFLFVBQVUsQ0FBQ0MsSUFBTXhFLGNBQWN3RSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQzdDSSxhQUFZOzRDQUNabEMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtoQiw4REFBQ3NCOzs4Q0FDQyw4REFBQ0s7b0NBQU0zQixXQUFVOzhDQUErQzs7Ozs7OzhDQUNoRSw4REFBQ21DO29DQUNDTCxPQUFPckU7b0NBQ1BzRSxVQUFVLENBQUNDLElBQU10RSxjQUFjc0UsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29DQUM3QzlCLFdBQVU7O3NEQUVWLDhEQUFDb0M7NENBQU9OLE9BQU07c0RBQU07Ozs7OztzREFDcEIsOERBQUNNOzRDQUFPTixPQUFNO3NEQUFjOzs7Ozs7c0RBQzVCLDhEQUFDTTs0Q0FBT04sT0FBTTtzREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUkxQiw4REFBQ1I7OzhDQUNDLDhEQUFDSztvQ0FBTTNCLFdBQVU7OENBQStDOzs7Ozs7OENBQ2hFLDhEQUFDbUM7b0NBQ0NMLE9BQU9uRTtvQ0FDUG9FLFVBQVUsQ0FBQ0MsSUFBTXBFLGdCQUFnQm9FLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvQ0FDL0M5QixXQUFVOztzREFFViw4REFBQ29DOzRDQUFPTixPQUFNO3NEQUFNOzs7Ozs7c0RBQ3BCLDhEQUFDTTs0Q0FBT04sT0FBTTtzREFBVzs7Ozs7O3NEQUN6Qiw4REFBQ007NENBQU9OLE9BQU07c0RBQVk7Ozs7OztzREFDMUIsOERBQUNNOzRDQUFPTixPQUFNO3NEQUFVOzs7Ozs7c0RBQ3hCLDhEQUFDTTs0Q0FBT04sT0FBTTtzREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2hDLDhEQUFDUjtnQkFBSXRCLFdBQVU7O2tDQUNiLDhEQUFDc0I7d0JBQUl0QixXQUFVO2tDQUNiLDRFQUFDcUM7NEJBQUdyQyxXQUFVOztnQ0FBc0M7Z0NBQ2xDVyxlQUFlakIsTUFBTTtnQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUkxQyw4REFBQzRCO3dCQUFJdEIsV0FBVTs7MENBQ2IsOERBQUNzQztnQ0FBTXRDLFdBQVU7O2tEQUNmLDhEQUFDdUM7d0NBQU12QyxXQUFVO2tEQUNmLDRFQUFDd0M7OzhEQUNDLDhEQUFDQztvREFBR3pDLFdBQVU7OERBQWtGOzs7Ozs7OERBR2hHLDhEQUFDeUM7b0RBQUd6QyxXQUFVOzhEQUFrRjs7Ozs7OzhEQUdoRyw4REFBQ3lDO29EQUFHekMsV0FBVTs4REFBa0Y7Ozs7Ozs4REFHaEcsOERBQUN5QztvREFBR3pDLFdBQVU7OERBQWtGOzs7Ozs7OERBR2hHLDhEQUFDeUM7b0RBQUd6QyxXQUFVOzhEQUFrRjs7Ozs7OzhEQUdoRyw4REFBQ3lDO29EQUFHekMsV0FBVTs4REFBa0Y7Ozs7Ozs4REFHaEcsOERBQUN5QztvREFBR3pDLFdBQVU7OERBQWtGOzs7Ozs7OERBR2hHLDhEQUFDeUM7b0RBQUd6QyxXQUFVOzhEQUFrRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3BHLDhEQUFDMEM7d0NBQU0xQyxXQUFVO2tEQUNkVyxlQUFlZ0MsR0FBRyxDQUFDLENBQUM5QjtnREFPTkEseUJBQUFBLGdCQUFzQ0EsY0FLU0EsaUJBRW5EQSxpQkFtQ0pBO2lFQWhETCw4REFBQzJCO2dEQUFrQnhDLFdBQVU7O2tFQUMzQiw4REFBQzRDO3dEQUFHNUMsV0FBVTtrRUFDWiw0RUFBQ3NCOzREQUFJdEIsV0FBVTs7OEVBQ2IsOERBQUNzQjtvRUFBSXRCLFdBQVU7OEVBQ2IsNEVBQUNzQjt3RUFBSXRCLFdBQVU7a0ZBQ2IsNEVBQUM2Qzs0RUFBSzdDLFdBQVU7c0ZBQ2JhLEVBQUFBLGlCQUFBQSxNQUFNSyxPQUFPLGNBQWJMLHNDQUFBQSwwQkFBQUEsZUFBZU0sUUFBUSxjQUF2Qk4sOENBQUFBLHdCQUF5QmlDLE1BQU0sQ0FBQyxTQUFNakMsZUFBQUEsTUFBTUUsS0FBSyxjQUFYRixtQ0FBQUEsYUFBYWlDLE1BQU0sQ0FBQyxPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OzhFQUl2RSw4REFBQ3hCO29FQUFJdEIsV0FBVTs7c0ZBQ2IsOERBQUNzQjs0RUFBSXRCLFdBQVU7c0ZBQXFDYSxFQUFBQSxrQkFBQUEsTUFBTUssT0FBTyxjQUFiTCxzQ0FBQUEsZ0JBQWVNLFFBQVEsS0FBSU4sTUFBTUUsS0FBSzs7Ozs7O3NGQUMxRiw4REFBQ087NEVBQUl0QixXQUFVO3NGQUF5QmEsTUFBTUUsS0FBSzs7Ozs7O3dFQUNsREYsRUFBQUEsa0JBQUFBLE1BQU1LLE9BQU8sY0FBYkwsc0NBQUFBLGdCQUFla0MsS0FBSyxtQkFDbkIsOERBQUN6Qjs0RUFBSXRCLFdBQVU7OzhGQUNiLDhEQUFDcEUsMk1BQUtBO29GQUFDb0UsV0FBVTs7Ozs7O2dGQUNoQmEsTUFBTUssT0FBTyxDQUFDNkIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU05Qiw4REFBQ0g7d0RBQUc1QyxXQUFVO2tFQUNaLDRFQUFDc0I7NERBQUl0QixXQUFVOztnRUFDWkMsWUFBWVksTUFBTXRDLElBQUk7OEVBQ3ZCLDhEQUFDc0U7b0VBQUs3QyxXQUFXLDRCQUVoQixPQURDYSxNQUFNdEMsSUFBSSxLQUFLLGdCQUFnQixvQkFBb0I7OEVBRWxEc0MsTUFBTXRDLElBQUksS0FBSyxnQkFBZ0IsYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSW5ELDhEQUFDcUU7d0RBQUc1QyxXQUFVO2tFQUNaLDRFQUFDc0I7NERBQUl0QixXQUFVOztnRUFDWkQsY0FBY2MsTUFBTXJDLE1BQU07OEVBQzNCLDhEQUFDcUU7b0VBQUs3QyxXQUFXLGdCQUtoQixPQUpDYSxNQUFNckMsTUFBTSxLQUFLLGFBQWEsbUJBQzlCcUMsTUFBTXJDLE1BQU0sS0FBSyxjQUFjLGlCQUMvQnFDLE1BQU1yQyxNQUFNLEtBQUssWUFBWSxpQkFDN0I7OEVBRUNxQyxNQUFNckMsTUFBTSxLQUFLLGFBQWEsUUFDOUJxQyxNQUFNckMsTUFBTSxLQUFLLGNBQWMsU0FDL0JxQyxNQUFNckMsTUFBTSxLQUFLLFlBQVksVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSTlDLDhEQUFDb0U7d0RBQUc1QyxXQUFVO2tFQUNYYSxFQUFBQSxrQkFBQUEsTUFBTUssT0FBTyxjQUFiTCxzQ0FBQUEsZ0JBQWVtQyxVQUFVLEtBQUk7Ozs7OztrRUFFaEMsOERBQUNKO3dEQUFHNUMsV0FBVTtrRUFDWEUsZ0JBQWdCVyxNQUFNb0MsU0FBUzs7Ozs7O2tFQUVsQyw4REFBQ0w7d0RBQUc1QyxXQUFVO2tFQUNYYSxNQUFNcUMsU0FBUyxHQUFHaEQsZ0JBQWdCVyxNQUFNcUMsU0FBUyxJQUFJOzs7Ozs7a0VBRXhELDhEQUFDTjt3REFBRzVDLFdBQVU7a0VBQ1osNEVBQUNzQjs0REFBSXRCLFdBQVU7O2dFQUNaYSxNQUFNbEIsV0FBVyxDQUFDd0QsS0FBSyxDQUFDLEdBQUcsR0FBR1IsR0FBRyxDQUFDLENBQUNTLDJCQUNsQyw4REFBQ1A7d0VBRUM3QyxXQUFVO2tGQUVUb0QsV0FBV0MsT0FBTyxDQUFDLEtBQUs7dUVBSHBCRDs7Ozs7Z0VBTVJ2QyxNQUFNbEIsV0FBVyxDQUFDRCxNQUFNLEdBQUcsbUJBQzFCLDhEQUFDbUQ7b0VBQUs3QyxXQUFVOzt3RUFBZ0c7d0VBQzVHYSxNQUFNbEIsV0FBVyxDQUFDRCxNQUFNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFLckMsOERBQUNrRDt3REFBRzVDLFdBQVU7a0VBQ1osNEVBQUNzQjs0REFBSXRCLFdBQVU7OzhFQUNiLDhEQUFDeUI7b0VBQ0NDLFNBQVMsSUFBTXBFLGlCQUFpQnVEO29FQUNoQ2IsV0FBVTtvRUFDVnNELE9BQU07OEVBRU4sNEVBQUMvSCwyTUFBR0E7d0VBQUN5RSxXQUFVOzs7Ozs7Ozs7Ozs4RUFFakIsOERBQUN5QjtvRUFDQ0MsU0FBUzt3RUFDUHJFLGdCQUFnQndEO3dFQUNoQjFELGlCQUFpQjtvRUFDbkI7b0VBQ0E2QyxXQUFVO29FQUNWc0QsT0FBTTs4RUFFTiw0RUFBQ2pJLDJNQUFJQTt3RUFBQzJFLFdBQVU7Ozs7Ozs7Ozs7O2dFQUVqQmEsTUFBTXJDLE1BQU0sS0FBSywyQkFDaEIsOERBQUNpRDtvRUFDQ3pCLFdBQVU7b0VBQ1ZzRCxPQUFNOzhFQUVOLDRFQUFDOUgsMk1BQUlBO3dFQUFDd0UsV0FBVTs7Ozs7Ozs7Ozt5RkFHbEIsOERBQUN5QjtvRUFDQ3pCLFdBQVU7b0VBQ1ZzRCxPQUFNOzhFQUVOLDRFQUFDN0gsMk1BQU1BO3dFQUFDdUUsV0FBVTs7Ozs7Ozs7Ozs7Z0VBR3JCYSxNQUFNdEMsSUFBSSxLQUFLLCtCQUNkLDhEQUFDa0Q7b0VBQ0N6QixXQUFVO29FQUNWc0QsT0FBTTs4RUFFTiw0RUFBQ2hJLDJNQUFNQTt3RUFBQzBFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQWhIbkJhLE1BQU0wQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs0QkEwSHRCNUMsZUFBZWpCLE1BQU0sS0FBSyxtQkFDekIsOERBQUM0QjtnQ0FBSXRCLFdBQVU7O2tEQUNiLDhEQUFDN0UsME1BQU1BO3dDQUFDNkUsV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ3VCO3dDQUFFdkIsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9wQ2hELGlDQUNDLDhEQUFDOUIseUZBQTBCQTtnQkFDekJzSSxRQUFReEc7Z0JBQ1J5RyxTQUFTLElBQU14RyxtQkFBbUI7Z0JBQ2xDeUcsV0FBVztvQkFDVHpHLG1CQUFtQjtvQkFDbkJjLGNBQWMseUJBQXlCOztnQkFDekM7Ozs7OztZQUtIekIsK0JBQ0MsOERBQUNnRjtnQkFBSXRCLFdBQVU7MEJBQ2IsNEVBQUNzQjtvQkFBSXRCLFdBQVU7O3NDQUViLDhEQUFDc0I7NEJBQUl0QixXQUFVOzs4Q0FDYiw4REFBQ3NCO29DQUFJdEIsV0FBVTs7c0RBQ2IsOERBQUN6RSwyTUFBR0E7NENBQUN5RSxXQUFVOzs7Ozs7c0RBQ2YsOERBQUMyRDs0Q0FBRzNELFdBQVU7c0RBQWtDOzs7Ozs7Ozs7Ozs7OENBRWxELDhEQUFDeUI7b0NBQ0NDLFNBQVMsSUFBTXBFLGlCQUFpQjtvQ0FDaEMwQyxXQUFVOzhDQUVWLDRFQUFDOUQsMk1BQUNBO3dDQUFDOEQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS2pCLDhEQUFDc0I7NEJBQUl0QixXQUFVOzs4Q0FFYiw4REFBQ3NCO29DQUFJdEIsV0FBVTs7c0RBQ2IsOERBQUNzQjs7OERBQ0MsOERBQUNLO29EQUFNM0IsV0FBVTs4REFBK0M7Ozs7Ozs4REFDaEUsOERBQUNzQjtvREFBSXRCLFdBQVU7O3NFQUNiLDhEQUFDN0QsMk1BQUlBOzREQUFDNkQsV0FBVTs7Ozs7O3NFQUNoQiw4REFBQzZDOzREQUFLN0MsV0FBVTtzRUFBaUIxRCxFQUFBQSx5QkFBQUEsY0FBYzRFLE9BQU8sY0FBckI1RSw2Q0FBQUEsdUJBQXVCNkUsUUFBUSxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSXhFLDhEQUFDRzs7OERBQ0MsOERBQUNLO29EQUFNM0IsV0FBVTs4REFBK0M7Ozs7Ozs4REFDaEUsOERBQUNzQjtvREFBSXRCLFdBQVU7O3NFQUNiLDhEQUFDckUsMk1BQUlBOzREQUFDcUUsV0FBVTs7Ozs7O3NFQUNoQiw4REFBQzZDOzREQUFLN0MsV0FBVTtzRUFBaUIxRCxjQUFjeUUsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUl4RCw4REFBQ087OzhEQUNDLDhEQUFDSztvREFBTTNCLFdBQVU7OERBQStDOzs7Ozs7OERBQ2hFLDhEQUFDc0I7b0RBQUl0QixXQUFVOztzRUFDYiw4REFBQ3BFLDJNQUFLQTs0REFBQ29FLFdBQVU7Ozs7OztzRUFDakIsOERBQUM2Qzs0REFBSzdDLFdBQVU7c0VBQWlCMUQsRUFBQUEsMEJBQUFBLGNBQWM0RSxPQUFPLGNBQXJCNUUsOENBQUFBLHdCQUF1QnlHLEtBQUssS0FBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUlyRSw4REFBQ3pCOzs4REFDQyw4REFBQ0s7b0RBQU0zQixXQUFVOzhEQUErQzs7Ozs7OzhEQUNoRSw4REFBQ3NCO29EQUFJdEIsV0FBVTs7c0VBQ2IsOERBQUM1RCwyTUFBUUE7NERBQUM0RCxXQUFVOzs7Ozs7c0VBQ3BCLDhEQUFDNkM7NERBQUs3QyxXQUFVO3NFQUFpQjFELEVBQUFBLDBCQUFBQSxjQUFjNEUsT0FBTyxjQUFyQjVFLDhDQUFBQSx3QkFBdUIwRyxVQUFVLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNNUUsOERBQUMxQjtvQ0FBSXRCLFdBQVU7O3NEQUNiLDhEQUFDc0I7OzhEQUNDLDhEQUFDSztvREFBTTNCLFdBQVU7OERBQStDOzs7Ozs7OERBQ2hFLDhEQUFDc0I7b0RBQUl0QixXQUFVOzt3REFDWkMsWUFBWTNELGNBQWNpQyxJQUFJO3NFQUMvQiw4REFBQ3NFOzREQUFLN0MsV0FBVyxvQkFFaEIsT0FEQzFELGNBQWNpQyxJQUFJLEtBQUssZ0JBQWdCLG9CQUFvQjtzRUFFMURqQyxjQUFjaUMsSUFBSSxLQUFLLGdCQUFnQixhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSzNELDhEQUFDK0M7OzhEQUNDLDhEQUFDSztvREFBTTNCLFdBQVU7OERBQStDOzs7Ozs7OERBQ2hFLDhEQUFDc0I7b0RBQUl0QixXQUFVOzt3REFDWkQsY0FBY3pELGNBQWNrQyxNQUFNO3NFQUNuQyw4REFBQ3FFOzREQUFLN0MsV0FBVyxvQkFLaEIsT0FKQzFELGNBQWNrQyxNQUFNLEtBQUssYUFBYSxtQkFDdENsQyxjQUFja0MsTUFBTSxLQUFLLGNBQWMsaUJBQ3ZDbEMsY0FBY2tDLE1BQU0sS0FBSyxZQUFZLGlCQUNyQztzRUFFQ2xDLGNBQWNrQyxNQUFNLEtBQUssYUFBYSxRQUN0Q2xDLGNBQWNrQyxNQUFNLEtBQUssY0FBYyxTQUN2Q2xDLGNBQWNrQyxNQUFNLEtBQUssWUFBWSxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT3hELDhEQUFDOEM7b0NBQUl0QixXQUFVOztzREFDYiw4REFBQ3NCOzs4REFDQyw4REFBQ0s7b0RBQU0zQixXQUFVOzhEQUErQzs7Ozs7OzhEQUNoRSw4REFBQ3NCO29EQUFJdEIsV0FBVTs7c0VBQ2IsOERBQUNuRSwyTUFBUUE7NERBQUNtRSxXQUFVOzs7Ozs7c0VBQ3BCLDhEQUFDNkM7NERBQUs3QyxXQUFVO3NFQUFpQkUsZ0JBQWdCNUQsY0FBYzJHLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJNUUsOERBQUMzQjs7OERBQ0MsOERBQUNLO29EQUFNM0IsV0FBVTs4REFBK0M7Ozs7Ozs4REFDaEUsOERBQUNzQjtvREFBSXRCLFdBQVU7O3NFQUNiLDhEQUFDbkUsMk1BQVFBOzREQUFDbUUsV0FBVTs7Ozs7O3NFQUNwQiw4REFBQzZDOzREQUFLN0MsV0FBVTtzRUFDYjFELGNBQWM0RyxTQUFTLEdBQUdoRCxnQkFBZ0I1RCxjQUFjNEcsU0FBUyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTzlFLDhEQUFDNUI7O3NEQUNDLDhEQUFDSzs0Q0FBTTNCLFdBQVU7c0RBQStDOzs7Ozs7c0RBQ2hFLDhEQUFDc0I7NENBQUl0QixXQUFVOztnREFDWjFELGNBQWNxRCxXQUFXLENBQUNnRCxHQUFHLENBQUMsQ0FBQ1MsMkJBQzlCLDhEQUFDUDt3REFFQzdDLFdBQVU7a0VBRVRvRCxXQUFXQyxPQUFPLENBQUMsS0FBSzt1REFIcEJEOzs7OztnREFNUjlHLGNBQWNxRCxXQUFXLENBQUNELE1BQU0sS0FBSyxtQkFDcEMsOERBQUNtRDtvREFBSzdDLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBTTdDMUQsY0FBY3NILFNBQVMsa0JBQ3RCLDhEQUFDdEM7O3NEQUNDLDhEQUFDSzs0Q0FBTTNCLFdBQVU7c0RBQStDOzs7Ozs7c0RBQ2hFLDhEQUFDc0I7NENBQUl0QixXQUFVOzs4REFDYiw4REFBQzdELDJNQUFJQTtvREFBQzZELFdBQVU7Ozs7Ozs4REFDaEIsOERBQUM2QztvREFBSzdDLFdBQVU7OERBQWlCMUQsY0FBY3NILFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPaEUsOERBQUN0Qzs0QkFBSXRCLFdBQVU7c0NBQ2IsNEVBQUN5QjtnQ0FDQ0MsU0FBUyxJQUFNcEUsaUJBQWlCO2dDQUNoQzBDLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTZjtHQW5tQndCM0Q7O1FBQ1NwQiw2REFBWUE7OztLQURyQm9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9zdXBlcmFkbWluL2FkbWluLW1hbmFnZW1lbnQvcGFnZS50c3g/MjBkNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlQWRtaW5BdXRoIH0gZnJvbSAnQC9ob29rcy91c2VBZG1pbkF1dGgnXG5pbXBvcnQgU3VwZXJhZG1pbkNyZWF0ZUFkbWluTW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL3N1cGVyYWRtaW4vU3VwZXJhZG1pbkNyZWF0ZUFkbWluTW9kYWwnXG5pbXBvcnQgU3VwZXJhZG1pbkVkaXRBZG1pbk1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9zdXBlcmFkbWluL1N1cGVyYWRtaW5FZGl0QWRtaW5Nb2RhbCdcbmltcG9ydCB7XG4gIFNoaWVsZCxcbiAgVXNlclBsdXMsXG4gIEVkaXQsXG4gIFRyYXNoMixcbiAgRXllLFxuICBMb2NrLFxuICBVbmxvY2ssXG4gIENyb3duLFxuICBTZXR0aW5ncyxcbiAgTWFpbCxcbiAgUGhvbmUsXG4gIENhbGVuZGFyLFxuICBDaGVja0NpcmNsZSxcbiAgWENpcmNsZSxcbiAgQWxlcnRUcmlhbmdsZSxcbiAgU2VhcmNoLFxuICBGaWx0ZXIsXG4gIFgsXG4gIFVzZXIsXG4gIEJ1aWxkaW5nXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIEFkbWluVXNlciB7XG4gIGlkOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICByb2xlOiAnYWRtaW4nIHwgJ3N1cGVyX2FkbWluJ1xuICBzdGF0dXM6ICdhcHByb3ZlZCcgfCAnc3VzcGVuZGVkJyB8ICdibG9ja2VkJyB8ICdwZW5kaW5nJyB8ICdkb2N1bWVudHNfc3VibWl0dGVkJyB8ICd1bmRlcl9yZXZpZXcnXG4gIHByb2ZpbGU6IHtcbiAgICBmdWxsTmFtZTogc3RyaW5nXG4gICAgcGhvbmU/OiBzdHJpbmdcbiAgICBkZXBhcnRtZW50Pzogc3RyaW5nXG4gIH1cbiAgcGVybWlzc2lvbnM6IHN0cmluZ1tdXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIGxhc3RMb2dpbj86IHN0cmluZ1xuICBjcmVhdGVkQnk/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIEFkbWluU3RhdHMge1xuICB0b3RhbEFkbWluczogbnVtYmVyXG4gIGFjdGl2ZUFkbWluczogbnVtYmVyXG4gIHN1c3BlbmRlZEFkbWluczogbnVtYmVyXG4gIHN1cGVyQWRtaW5zOiBudW1iZXJcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3VwZXJBZG1pbk1hbmFnZW1lbnRQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIGlzQXV0aG9yaXplZCB9ID0gdXNlQWRtaW5BdXRoKHsgcmVxdWlyZWRSb2xlOiAnc3VwZXJfYWRtaW4nIH0pXG4gIGNvbnN0IFthZG1pbnMsIHNldEFkbWluc10gPSB1c2VTdGF0ZTxBZG1pblVzZXJbXT4oW10pXG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8QWRtaW5TdGF0cyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzaG93Q3JlYXRlTW9kYWwsIHNldFNob3dDcmVhdGVNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dFZGl0TW9kYWwsIHNldFNob3dFZGl0TW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtlZGl0aW5nQWRtaW4sIHNldEVkaXRpbmdBZG1pbl0gPSB1c2VTdGF0ZTxBZG1pblVzZXIgfCBudWxsPihudWxsKVxuXG4gIC8vIERvbid0IHJlbmRlciBpZiB1c2VyIGlzIG5vdCBhdXRob3JpemVkXG4gIGlmICghaXNBdXRob3JpemVkKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuICBjb25zdCBbc2VsZWN0ZWRBZG1pbiwgc2V0U2VsZWN0ZWRBZG1pbl0gPSB1c2VTdGF0ZTxBZG1pblVzZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2ZpbHRlclJvbGUsIHNldEZpbHRlclJvbGVdID0gdXNlU3RhdGU8c3RyaW5nPignYWxsJylcbiAgY29uc3QgW2ZpbHRlclN0YXR1cywgc2V0RmlsdGVyU3RhdHVzXSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpXG4gIGNvbnN0IFtoYXNJbml0aWFsaXplZCwgc2V0SGFzSW5pdGlhbGl6ZWRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBPbmx5IGZldGNoIG9uY2Ugd2hlbiB1c2VyIGlzIGF1dGhvcml6ZWQgYW5kIHdlIGhhdmVuJ3QgaW5pdGlhbGl6ZWQgeWV0XG4gICAgaWYgKGlzQXV0aG9yaXplZCAmJiAhaGFzSW5pdGlhbGl6ZWQpIHtcbiAgICAgIHNldEhhc0luaXRpYWxpemVkKHRydWUpXG4gICAgICBmZXRjaEFkbWlucygpXG4gICAgfVxuICB9LCBbaXNBdXRob3JpemVkLCBoYXNJbml0aWFsaXplZF0pIC8vIE9ubHkgZGVwZW5kIG9uIGF1dGhvcml6YXRpb24gYW5kIGluaXRpYWxpemF0aW9uIHN0YXRlXG5cbiAgLy8gU2VwYXJhdGUgdXNlRWZmZWN0IGZvciBmaWx0ZXIgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChoYXNJbml0aWFsaXplZCkge1xuICAgICAgZmV0Y2hBZG1pbnMoKVxuICAgIH1cbiAgfSwgW2ZpbHRlclJvbGUsIGZpbHRlclN0YXR1cywgc2VhcmNoVGVybV0pIC8vIEZldGNoIHdoZW4gZmlsdGVycyBjaGFuZ2VcblxuICBjb25zdCBmZXRjaEFkbWlucyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKVxuXG4gICAgICAvLyBGZXRjaCByZWFsIGFkbWluIGRhdGEgZnJvbSBiYWNrZW5kIEFQSVxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICAgIHBhZ2U6ICcxJyxcbiAgICAgICAgbGltaXQ6ICc1MCcsXG4gICAgICAgIHJvbGU6IGZpbHRlclJvbGUgIT09ICdhbGwnID8gZmlsdGVyUm9sZSA6ICcnLFxuICAgICAgICBzdGF0dXM6IGZpbHRlclN0YXR1cyAhPT0gJ2FsbCcgPyBmaWx0ZXJTdGF0dXMgOiAnJyxcbiAgICAgICAgc2VhcmNoOiBzZWFyY2hUZXJtXG4gICAgICB9KVxuXG4gICAgICBjb25zb2xlLmxvZygnTWFraW5nIEFQSSBjYWxsIHRvOicsIGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3N1cGVyYWRtaW4vYWRtaW5zPyR7cGFyYW1zfWApXG4gICAgICBjb25zb2xlLmxvZygnVG9rZW46JywgdG9rZW4gPyAnUHJlc2VudCcgOiAnTWlzc2luZycpXG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvc3VwZXJhZG1pbi9hZG1pbnM/JHtwYXJhbXN9YCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIGNvbnNvbGUubG9nKCdSZXNwb25zZSBzdGF0dXM6JywgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KVxuICAgICAgY29uc29sZS5sb2coJ1Jlc3BvbnNlLm9rOicsIHJlc3BvbnNlLm9rKVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBBUEkgY2FsbCBzdWNjZXNzZnVsLCBwcm9jZXNzaW5nIHJlc3BvbnNlLi4uJylcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICAgIGNvbnNvbGUubG9nKCdBUEkgUmVzcG9uc2U6JywgcmVzdWx0KVxuICAgICAgICBjb25zb2xlLmxvZygnQWRtaW5zIGRhdGE6JywgcmVzdWx0LmRhdGE/LmFkbWlucylcbiAgICAgICAgY29uc29sZS5sb2coJ1N0YXRzIGRhdGE6JywgcmVzdWx0LmRhdGE/LnN0YXRzKVxuXG4gICAgICAgIGNvbnN0IGFkbWluc0RhdGEgPSByZXN1bHQuZGF0YT8uYWRtaW5zIHx8IFtdXG4gICAgICAgIGNvbnN0IHN0YXRzRGF0YSA9IHJlc3VsdC5kYXRhPy5zdGF0cyB8fCB7XG4gICAgICAgICAgdG90YWxBZG1pbnM6IDAsXG4gICAgICAgICAgYWN0aXZlQWRtaW5zOiAwLFxuICAgICAgICAgIHN1c3BlbmRlZEFkbWluczogMCxcbiAgICAgICAgICBzdXBlckFkbWluczogMFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1NldHRpbmcgYWRtaW5zIGRhdGE6JywgYWRtaW5zRGF0YS5sZW5ndGgsICdhZG1pbnMnKVxuICAgICAgICBjb25zb2xlLmxvZygnRmlyc3QgYWRtaW4gc2FtcGxlOicsIGFkbWluc0RhdGFbMF0pXG4gICAgICAgIGNvbnNvbGUubG9nKCdGaXJzdCBhZG1pbiBwZXJtaXNzaW9uczonLCBhZG1pbnNEYXRhWzBdPy5wZXJtaXNzaW9ucylcbiAgICAgICAgY29uc29sZS5sb2coJ1NldHRpbmcgc3RhdHMgZGF0YTonLCBzdGF0c0RhdGEpXG5cbiAgICAgICAgc2V0QWRtaW5zKGFkbWluc0RhdGEpXG4gICAgICAgIHNldFN0YXRzKHN0YXRzRGF0YSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEFQSSBjYWxsIGZhaWxlZCAtIHNob3cgZW1wdHkgZGF0YSBpbnN0ZWFkIG9mIGZha2UgZGF0YVxuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQVBJIGNhbGwgZmFpbGVkJywgcmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0KVxuICAgICAgICBjb25zdCBlcnJvclRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KClcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVzcG9uc2U6JywgZXJyb3JUZXh0KVxuXG4gICAgICAgIHNldEFkbWlucyhbXSlcbiAgICAgICAgc2V0U3RhdHMoe1xuICAgICAgICAgIHRvdGFsQWRtaW5zOiAwLFxuICAgICAgICAgIGFjdGl2ZUFkbWluczogMCxcbiAgICAgICAgICBzdXNwZW5kZWRBZG1pbnM6IDAsXG4gICAgICAgICAgc3VwZXJBZG1pbnM6IDBcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYWRtaW5zOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFN0YXRzIGFyZSBub3cgZmV0Y2hlZCB0b2dldGhlciB3aXRoIGFkbWluc1xuXG4gIGNvbnN0IGdldFN0YXR1c0ljb24gPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnYXBwcm92ZWQnOlxuICAgICAgICByZXR1cm4gPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgY2FzZSAnc3VzcGVuZGVkJzpcbiAgICAgICAgcmV0dXJuIDxYQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgIGNhc2UgJ2Jsb2NrZWQnOlxuICAgICAgICByZXR1cm4gPFhDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC02MDBcIiAvPlxuICAgICAgY2FzZSAncGVuZGluZyc6XG4gICAgICBjYXNlICdkb2N1bWVudHNfc3VibWl0dGVkJzpcbiAgICAgIGNhc2UgJ3VuZGVyX3Jldmlldyc6XG4gICAgICAgIHJldHVybiA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTUwMFwiIC8+XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRSb2xlSWNvbiA9IChyb2xlOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gcm9sZSA9PT0gJ3N1cGVyX2FkbWluJyA/IFxuICAgICAgPENyb3duIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1wdXJwbGUtNTAwXCIgLz4gOiBcbiAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgfVxuXG4gIGNvbnN0IGZvcm1hdFRpbWVzdGFtcCA9ICh0aW1lc3RhbXA6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZSh0aW1lc3RhbXApLnRvTG9jYWxlU3RyaW5nKCdhci1TQScsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgIH0pXG4gIH1cblxuICBjb25zdCBmaWx0ZXJlZEFkbWlucyA9IGFkbWlucy5maWx0ZXIoYWRtaW4gPT4ge1xuICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBzZWFyY2hUZXJtID09PSAnJyB8fFxuICAgICAgYWRtaW4uZW1haWwudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAoYWRtaW4ucHJvZmlsZT8uZnVsbE5hbWUgfHwgJycpLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKVxuICAgIGNvbnN0IG1hdGNoZXNSb2xlID0gZmlsdGVyUm9sZSA9PT0gJ2FsbCcgfHwgYWRtaW4ucm9sZSA9PT0gZmlsdGVyUm9sZVxuICAgIGNvbnN0IG1hdGNoZXNTdGF0dXMgPSBmaWx0ZXJTdGF0dXMgPT09ICdhbGwnIHx8IGFkbWluLnN0YXR1cyA9PT0gZmlsdGVyU3RhdHVzXG5cbiAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzUm9sZSAmJiBtYXRjaGVzU3RhdHVzXG4gIH0pXG5cbiAgLy8gU2hvdyBsb2FkaW5nIHNjcmVlbiB3aGlsZSBmZXRjaGluZyBkYXRhXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPtis2KfYsdmKINiq2K3ZhdmK2YQg2KfZhNmF2K/Ysdin2KEuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIHAtNlwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtYmx1ZS02MDAgbWwtM1wiIC8+XG4gICAgICAgICAgICAgINil2K/Yp9ix2Kkg2KfZhNmF2K/Ysdin2KFcbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTJcIj7Ypdiv2KfYsdipINit2LPYp9io2KfYqiDYp9mE2YXYr9ix2KfYoSDZiNin2YTYtdmE2KfYrdmK2KfYqjwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVc2VyUGx1cyBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAg2KXYttin2YHYqSDZhdiv2YrYsSDYrNiv2YrYr1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogU3RhdHMgQ2FyZHMgKi99XG4gICAgICB7c3RhdHMgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gcC02IGJvcmRlciBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2KXYrNmF2KfZhNmKINin2YTZhdiv2LHYp9ihPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3N0YXRzLnRvdGFsQWRtaW5zfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yp9mE2YXYr9ix2KfYoSDYp9mE2YbYtNi32YjZhjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57c3RhdHMuYWN0aXZlQWRtaW5zfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yp9mE2YXYr9ix2KfYoSDYp9mE2YXYudmE2YLZiNmGPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXJlZC02MDBcIj57c3RhdHMuc3VzcGVuZGVkQWRtaW5zfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxYQ2lyY2xlIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Yp9mE2YXYr9ix2KfYoSDYp9mE2LnYp9mF2YjZhjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwXCI+e3N0YXRzLnN1cGVyQWRtaW5zfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtcHVycGxlLTUwMFwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogRmlsdGVycyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gcC02IG1iLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2KfZhNio2K3YqzwvbGFiZWw+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2KfZhNio2K3YqyDYqNin2YTYp9iz2YUg2KPZiCDYp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2YouLi5cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci0xMCBwbC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7Yp9mE2K/ZiNixPC9sYWJlbD5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlclJvbGV9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyUm9sZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj7YrNmF2YrYuSDYp9mE2KPYr9mI2KfYsTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwic3VwZXJfYWRtaW5cIj7Zhdiv2YrYsSDYudin2YU8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFkbWluXCI+2YXYr9mK2LE8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPtin2YTYrdin2YTYqTwvbGFiZWw+XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJTdGF0dXN9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyU3RhdHVzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFsbFwiPtis2YXZiti5INin2YTYrdin2YTYp9iqPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhcHByb3ZlZFwiPtmG2LTYtzwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwic3VzcGVuZGVkXCI+2YXYudmE2YI8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJsb2NrZWRcIj7Zhdit2LjZiNixPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwZW5kaW5nXCI+2YHZiiDYp9mE2KfZhtiq2LjYp9ixPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFkbWlucyBUYWJsZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgINmC2KfYptmF2Kkg2KfZhNmF2K/Ysdin2KEgKHtmaWx0ZXJlZEFkbWlucy5sZW5ndGh9KVxuICAgICAgICAgIDwvaDM+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICDYp9mE2YXYr9mK2LFcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICDYp9mE2K/ZiNixXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAg2KfZhNit2KfZhNipXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAg2KfZhNmC2LPZhVxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LXJpZ2h0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgINiq2KfYsdmK2K4g2KfZhNil2YbYtNin2KFcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICDYotiu2LEg2K/YrtmI2YRcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICDYp9mE2LXZhNin2K3Zitin2KpcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICDYpdis2LHYp9ih2KfYqlxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICB7ZmlsdGVyZWRBZG1pbnMubWFwKChhZG1pbikgPT4gKFxuICAgICAgICAgICAgICAgIDx0ciBrZXk9e2FkbWluLmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgaC0xMCB3LTEwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTAgdy0xMCByb3VuZGVkLWZ1bGwgYmctZ3JheS0zMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2FkbWluLnByb2ZpbGU/LmZ1bGxOYW1lPy5jaGFyQXQoMCkgfHwgYWRtaW4uZW1haWw/LmNoYXJBdCgwKSB8fCAnPyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57YWRtaW4ucHJvZmlsZT8uZnVsbE5hbWUgfHwgYWRtaW4uZW1haWx9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPnthZG1pbi5lbWFpbH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHthZG1pbi5wcm9maWxlPy5waG9uZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwIGZsZXggaXRlbXMtY2VudGVyIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiaC0zIHctMyBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWRtaW4ucHJvZmlsZS5waG9uZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Z2V0Um9sZUljb24oYWRtaW4ucm9sZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhZG1pbi5yb2xlID09PSAnc3VwZXJfYWRtaW4nID8gJ3RleHQtcHVycGxlLTYwMCcgOiAndGV4dC1ibHVlLTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YWRtaW4ucm9sZSA9PT0gJ3N1cGVyX2FkbWluJyA/ICfZhdiv2YrYsSDYudin2YUnIDogJ9mF2K/ZitixJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKGFkbWluLnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMiB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhZG1pbi5zdGF0dXMgPT09ICdhcHByb3ZlZCcgPyAndGV4dC1ncmVlbi02MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGFkbWluLnN0YXR1cyA9PT0gJ3N1c3BlbmRlZCcgPyAndGV4dC1yZWQtNjAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICBhZG1pbi5zdGF0dXMgPT09ICdibG9ja2VkJyA/ICd0ZXh0LXJlZC03MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LXllbGxvdy02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2FkbWluLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJyA/ICfZhti02LcnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICBhZG1pbi5zdGF0dXMgPT09ICdzdXNwZW5kZWQnID8gJ9mF2LnZhNmCJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgYWRtaW4uc3RhdHVzID09PSAnYmxvY2tlZCcgPyAn2YXYrdi42YjYsScgOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHthZG1pbi5wcm9maWxlPy5kZXBhcnRtZW50IHx8ICctJ31cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VGltZXN0YW1wKGFkbWluLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2FkbWluLmxhc3RMb2dpbiA/IGZvcm1hdFRpbWVzdGFtcChhZG1pbi5sYXN0TG9naW4pIDogJ9mE2YUg2YrYs9is2YQg2K/YrtmI2YQnfVxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHthZG1pbi5wZXJtaXNzaW9ucy5zbGljZSgwLCAyKS5tYXAoKHBlcm1pc3Npb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17cGVybWlzc2lvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Blcm1pc3Npb24ucmVwbGFjZSgnXycsICcgJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAge2FkbWluLnBlcm1pc3Npb25zLmxlbmd0aCA+IDIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LW1lZGl1bSBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICt7YWRtaW4ucGVybWlzc2lvbnMubGVuZ3RoIC0gMn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZEFkbWluKGFkbWluKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIti52LHYtiDYp9mE2KrZgdin2LXZitmEXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RWRpdGluZ0FkbWluKGFkbWluKVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93RWRpdE1vZGFsKHRydWUpXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgaG92ZXI6dGV4dC1ncmVlbi04MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLYqti52K/ZitmEXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICB7YWRtaW4uc3RhdHVzID09PSAnYXBwcm92ZWQnID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDAgaG92ZXI6dGV4dC15ZWxsb3ctODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLYqti52YTZitmCXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExvY2sgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdyZWVuLTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi2KXZhNi62KfYoSDYp9mE2KrYudmE2YrZglwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxVbmxvY2sgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHthZG1pbi5yb2xlICE9PSAnc3VwZXJfYWRtaW4nICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTgwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi2K3YsNmBXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgPC90YWJsZT5cblxuICAgICAgICAgIHtmaWx0ZXJlZEFkbWlucy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj7ZhNinINiq2YjYrNivINmF2K/Ysdin2KEg2KrYt9in2KjZgiDYp9mE2YXYudin2YrZitixINin2YTZhdit2K/Yr9ipPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENyZWF0ZSBBZG1pbiBNb2RhbCAqL31cbiAgICAgIHtzaG93Q3JlYXRlTW9kYWwgJiYgKFxuICAgICAgICA8U3VwZXJhZG1pbkNyZWF0ZUFkbWluTW9kYWxcbiAgICAgICAgICBpc09wZW49e3Nob3dDcmVhdGVNb2RhbH1cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93Q3JlYXRlTW9kYWwoZmFsc2UpfVxuICAgICAgICAgIG9uU3VjY2Vzcz17KCkgPT4ge1xuICAgICAgICAgICAgc2V0U2hvd0NyZWF0ZU1vZGFsKGZhbHNlKVxuICAgICAgICAgICAgZmV0Y2hBZG1pbnMoKSAvLyBSZWZyZXNoIHRoZSBhZG1pbiBsaXN0XG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBZG1pbiBEZXRhaWxzIE1vZGFsICovfVxuICAgICAge3NlbGVjdGVkQWRtaW4gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwIHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cteGwgbWF4LXctMnhsIHctZnVsbCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS02MDAgbWwtM1wiIC8+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj7YqtmB2KfYtdmK2YQg2KfZhNmF2K/ZitixPC9oMj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZEFkbWluKG51bGwpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgey8qIEJhc2ljIEluZm8gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7Yp9mE2KfYs9mFINin2YTZg9in2YXZhDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwIG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkQWRtaW4ucHJvZmlsZT8uZnVsbE5hbWUgfHwgJ9i62YrYsSDZhdit2K/Yryd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDAgbWwtMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj57c2VsZWN0ZWRBZG1pbi5lbWFpbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7YsdmC2YUg2KfZhNmH2KfYqtmBPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwIG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkQWRtaW4ucHJvZmlsZT8ucGhvbmUgfHwgJ9i62YrYsSDZhdit2K/Yryd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2KfZhNmC2LPZhTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntzZWxlY3RlZEFkbWluLnByb2ZpbGU/LmRlcGFydG1lbnQgfHwgJ9i62YrYsSDZhdit2K/Yryd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBSb2xlIGFuZCBTdGF0dXMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7Yp9mE2K/ZiNixPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgcC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICB7Z2V0Um9sZUljb24oc2VsZWN0ZWRBZG1pbi5yb2xlKX1cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMiBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQWRtaW4ucm9sZSA9PT0gJ3N1cGVyX2FkbWluJyA/ICd0ZXh0LXB1cnBsZS02MDAnIDogJ3RleHQtYmx1ZS02MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBZG1pbi5yb2xlID09PSAnc3VwZXJfYWRtaW4nID8gJ9mF2K/ZitixINi52KfZhScgOiAn2YXYr9mK2LEnfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7Yp9mE2K3Yp9mE2Kk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKHNlbGVjdGVkQWRtaW4uc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMiBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQWRtaW4uc3RhdHVzID09PSAnYXBwcm92ZWQnID8gJ3RleHQtZ3JlZW4tNjAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRBZG1pbi5zdGF0dXMgPT09ICdzdXNwZW5kZWQnID8gJ3RleHQtcmVkLTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQWRtaW4uc3RhdHVzID09PSAnYmxvY2tlZCcgPyAndGV4dC1yZWQtNzAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgJ3RleHQteWVsbG93LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFkbWluLnN0YXR1cyA9PT0gJ2FwcHJvdmVkJyA/ICfZhti02LcnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRBZG1pbi5zdGF0dXMgPT09ICdzdXNwZW5kZWQnID8gJ9mF2LnZhNmCJyA6XG4gICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQWRtaW4uc3RhdHVzID09PSAnYmxvY2tlZCcgPyAn2YXYrdi42YjYsScgOiAn2YHZiiDYp9mE2KfZhtiq2LjYp9ixJ31cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBUaW1lc3RhbXBzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+2KrYp9ix2YrYriDYp9mE2KXZhti02KfYoTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntmb3JtYXRUaW1lc3RhbXAoc2VsZWN0ZWRBZG1pbi5jcmVhdGVkQXQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPtii2K7YsSDYr9iu2YjZhDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFkbWluLmxhc3RMb2dpbiA/IGZvcm1hdFRpbWVzdGFtcChzZWxlY3RlZEFkbWluLmxhc3RMb2dpbikgOiAn2YTZhSDZitiz2KzZhCDYr9iu2YjZhCd9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogUGVybWlzc2lvbnMgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0zXCI+2KfZhNi12YTYp9it2YrYp9iqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRBZG1pbi5wZXJtaXNzaW9ucy5tYXAoKHBlcm1pc3Npb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e3Blcm1pc3Npb259XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtwZXJtaXNzaW9uLnJlcGxhY2UoJ18nLCAnICcpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZEFkbWluLnBlcm1pc3Npb25zLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPtmE2Kcg2KrZiNis2K8g2LXZhNin2K3Zitin2Kog2YXYrdiv2K/YqTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDcmVhdGVkIEJ5ICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWRBZG1pbi5jcmVhdGVkQnkgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7YqtmFINin2YTYpdmG2LTYp9ihINio2YjYp9iz2LfYqTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwIG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+e3NlbGVjdGVkQWRtaW4uY3JlYXRlZEJ5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgcC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRBZG1pbihudWxsKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctZ3JheS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgINil2LrZhNin2YJcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VBZG1pbkF1dGgiLCJTdXBlcmFkbWluQ3JlYXRlQWRtaW5Nb2RhbCIsIlNoaWVsZCIsIlVzZXJQbHVzIiwiRWRpdCIsIlRyYXNoMiIsIkV5ZSIsIkxvY2siLCJVbmxvY2siLCJDcm93biIsIk1haWwiLCJQaG9uZSIsIkNhbGVuZGFyIiwiQ2hlY2tDaXJjbGUiLCJYQ2lyY2xlIiwiQWxlcnRUcmlhbmdsZSIsIlNlYXJjaCIsIlgiLCJVc2VyIiwiQnVpbGRpbmciLCJTdXBlckFkbWluTWFuYWdlbWVudFBhZ2UiLCJzZWxlY3RlZEFkbWluIiwidXNlciIsImlzQXV0aG9yaXplZCIsInJlcXVpcmVkUm9sZSIsImFkbWlucyIsInNldEFkbWlucyIsInN0YXRzIiwic2V0U3RhdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNob3dDcmVhdGVNb2RhbCIsInNldFNob3dDcmVhdGVNb2RhbCIsInNob3dFZGl0TW9kYWwiLCJzZXRTaG93RWRpdE1vZGFsIiwiZWRpdGluZ0FkbWluIiwic2V0RWRpdGluZ0FkbWluIiwic2V0U2VsZWN0ZWRBZG1pbiIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwiZmlsdGVyUm9sZSIsInNldEZpbHRlclJvbGUiLCJmaWx0ZXJTdGF0dXMiLCJzZXRGaWx0ZXJTdGF0dXMiLCJoYXNJbml0aWFsaXplZCIsInNldEhhc0luaXRpYWxpemVkIiwiZmV0Y2hBZG1pbnMiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJwYWdlIiwibGltaXQiLCJyb2xlIiwic3RhdHVzIiwic2VhcmNoIiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJoZWFkZXJzIiwic3RhdHVzVGV4dCIsIm9rIiwicmVzdWx0IiwiYWRtaW5zRGF0YSIsImpzb24iLCJkYXRhIiwic3RhdHNEYXRhIiwidG90YWxBZG1pbnMiLCJhY3RpdmVBZG1pbnMiLCJzdXNwZW5kZWRBZG1pbnMiLCJzdXBlckFkbWlucyIsImxlbmd0aCIsInBlcm1pc3Npb25zIiwiZXJyb3IiLCJlcnJvclRleHQiLCJ0ZXh0IiwiZ2V0U3RhdHVzSWNvbiIsImNsYXNzTmFtZSIsImdldFJvbGVJY29uIiwiZm9ybWF0VGltZXN0YW1wIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImZpbHRlcmVkQWRtaW5zIiwiZmlsdGVyIiwiYWRtaW4iLCJtYXRjaGVzU2VhcmNoIiwiZW1haWwiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwicHJvZmlsZSIsImZ1bGxOYW1lIiwibWF0Y2hlc1JvbGUiLCJtYXRjaGVzU3RhdHVzIiwiZGl2IiwicCIsImgxIiwiYnV0dG9uIiwib25DbGljayIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInNlbGVjdCIsIm9wdGlvbiIsImgzIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsIm1hcCIsInRkIiwic3BhbiIsImNoYXJBdCIsInBob25lIiwiZGVwYXJ0bWVudCIsImNyZWF0ZWRBdCIsImxhc3RMb2dpbiIsInNsaWNlIiwicGVybWlzc2lvbiIsInJlcGxhY2UiLCJ0aXRsZSIsImlkIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU3VjY2VzcyIsImgyIiwiY3JlYXRlZEJ5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/superadmin/admin-management/page.tsx\n"));

/***/ })

});