"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/admin-management/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/calendar.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Calendar; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Calendar = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Calendar\", [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"4\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"eu3xkr\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"16\",\n            y1: \"2\",\n            y2: \"6\",\n            key: \"m3sa8f\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"8\",\n            y1: \"2\",\n            y2: \"6\",\n            key: \"18kwsl\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"3\",\n            x2: \"21\",\n            y1: \"10\",\n            y2: \"10\",\n            key: \"xt86sb\"\n        }\n    ]\n]);\n //# sourceMappingURL=calendar.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/superadmin/admin-management/page.tsx":
/*!**************************************************!*\
  !*** ./app/superadmin/admin-management/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SuperAdminManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAdminAuth */ \"(app-pages-browser)/./hooks/useAdminAuth.ts\");\n/* harmony import */ var _components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/superadmin/SuperadminCreateAdminModal */ \"(app-pages-browser)/./components/superadmin/SuperadminCreateAdminModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/unlock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Crown,Edit,Eye,Lock,Mail,Phone,Search,Shield,Trash2,Unlock,UserPlus,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SuperAdminManagementPage() {\n    var _selectedAdmin_profile, _selectedAdmin_profile1, _selectedAdmin_profile2;\n    _s();\n    const { user, isAuthorized } = (0,_hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth)({\n        requiredRole: \"super_admin\"\n    });\n    const [admins, setAdmins] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Don't render if user is not authorized\n    if (!isAuthorized) {\n        return null;\n    }\n    const [selectedAdmin, setSelectedAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filterRole, setFilterRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only fetch once when user is authorized and we haven't initialized yet\n        if (isAuthorized && !hasInitialized) {\n            setHasInitialized(true);\n            fetchAdmins();\n        }\n    }, [\n        isAuthorized,\n        hasInitialized\n    ]) // Only depend on authorization and initialization state\n    ;\n    // Separate useEffect for filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hasInitialized) {\n            fetchAdmins();\n        }\n    }, [\n        filterRole,\n        filterStatus,\n        searchTerm\n    ]) // Fetch when filters change\n    ;\n    const fetchAdmins = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real admin data from backend API\n            const token = localStorage.getItem(\"token\");\n            const params = new URLSearchParams({\n                page: \"1\",\n                limit: \"50\",\n                role: filterRole !== \"all\" ? filterRole : \"\",\n                status: filterStatus !== \"all\" ? filterStatus : \"\",\n                search: searchTerm\n            });\n            console.log(\"Making API call to:\", \"http://localhost:5000/api/superadmin/admins?\".concat(params));\n            console.log(\"Token:\", token ? \"Present\" : \"Missing\");\n            const response = await fetch(\"http://localhost:5000/api/superadmin/admins?\".concat(params), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            console.log(\"Response status:\", response.status, response.statusText);\n            console.log(\"Response.ok:\", response.ok);\n            if (response.ok) {\n                var _result_data, _result_data1, _result_data2, _result_data3, _adminsData_;\n                console.log(\"✅ API call successful, processing response...\");\n                const result = await response.json();\n                console.log(\"API Response:\", result);\n                console.log(\"Admins data:\", (_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.admins);\n                console.log(\"Stats data:\", (_result_data1 = result.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.stats);\n                const adminsData = ((_result_data2 = result.data) === null || _result_data2 === void 0 ? void 0 : _result_data2.admins) || [];\n                const statsData = ((_result_data3 = result.data) === null || _result_data3 === void 0 ? void 0 : _result_data3.stats) || {\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                };\n                console.log(\"Setting admins data:\", adminsData.length, \"admins\");\n                console.log(\"First admin sample:\", adminsData[0]);\n                console.log(\"First admin permissions:\", (_adminsData_ = adminsData[0]) === null || _adminsData_ === void 0 ? void 0 : _adminsData_.permissions);\n                console.log(\"Setting stats data:\", statsData);\n                setAdmins(adminsData);\n                setStats(statsData);\n            } else {\n                // API call failed - show empty data instead of fake data\n                console.error(\"❌ API call failed\", response.status, response.statusText);\n                const errorText = await response.text();\n                console.error(\"Error response:\", errorText);\n                setAdmins([]);\n                setStats({\n                    totalAdmins: 0,\n                    activeAdmins: 0,\n                    suspendedAdmins: 0,\n                    superAdmins: 0\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching admins:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Stats are now fetched together with admins\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            case \"suspended\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n            case \"blocked\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n            case \"documents_submitted\":\n            case \"under_review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleIcon = (role)=>{\n        return role === \"super_admin\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-4 w-4 text-purple-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-4 w-4 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp).toLocaleString(\"ar-SA\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const filteredAdmins = admins.filter((admin)=>{\n        var _admin_profile;\n        const matchesSearch = searchTerm === \"\" || admin.email.toLowerCase().includes(searchTerm.toLowerCase()) || (((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : _admin_profile.fullName) || \"\").toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesRole = filterRole === \"all\" || admin.role === filterRole;\n        const matchesStatus = filterStatus === \"all\" || admin.status === filterStatus;\n        return matchesSearch && matchesRole && matchesStatus;\n    });\n    // Show loading screen while fetching data\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل المدراء...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n            lineNumber: 199,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"إدارة المدراء\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"إدارة حسابات المدراء والصلاحيات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowCreateModal(true),\n                            className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                \"إضافة مدير جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"إجمالي المدراء\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stats.totalAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء النشطون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: stats.activeAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء المعلقون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: stats.suspendedAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-6 border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"المدراء العامون\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-purple-600\",\n                                            children: stats.superAdmins\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"البحث\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            placeholder: \"البحث بالاسم أو البريد الإلكتروني...\",\n                                            className: \"w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الدور\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterRole,\n                                    onChange: (e)=>setFilterRole(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الأدوار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"super_admin\",\n                                            children: \"مدير عام\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"admin\",\n                                            children: \"مدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"الحالة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filterStatus,\n                                    onChange: (e)=>setFilterStatus(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"جميع الحالات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"approved\",\n                                            children: \"نشط\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"suspended\",\n                                            children: \"معلق\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"blocked\",\n                                            children: \"محظور\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"pending\",\n                                            children: \"في الانتظار\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: [\n                                \"قائمة المدراء (\",\n                                filteredAdmins.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        className: \"bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"المدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"الصلاحيات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                    children: \"إجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        className: \"bg-white divide-y divide-gray-200\",\n                                        children: filteredAdmins.map((admin)=>{\n                                            var _admin_profile_fullName, _admin_profile, _admin_email, _admin_profile1, _admin_profile2, _admin_profile3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 h-10 w-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-700\",\n                                                                            children: ((_admin_profile = admin.profile) === null || _admin_profile === void 0 ? void 0 : (_admin_profile_fullName = _admin_profile.fullName) === null || _admin_profile_fullName === void 0 ? void 0 : _admin_profile_fullName.charAt(0)) || ((_admin_email = admin.email) === null || _admin_email === void 0 ? void 0 : _admin_email.charAt(0)) || \"?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mr-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900\",\n                                                                            children: ((_admin_profile1 = admin.profile) === null || _admin_profile1 === void 0 ? void 0 : _admin_profile1.fullName) || admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: admin.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_admin_profile2 = admin.profile) === null || _admin_profile2 === void 0 ? void 0 : _admin_profile2.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 flex items-center mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"h-3 w-3 ml-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                                    lineNumber: 378,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                admin.profile.phone\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getRoleIcon(admin.role),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm font-medium \".concat(admin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                                    children: admin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                getStatusIcon(admin.status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm \".concat(admin.status === \"approved\" ? \"text-green-600\" : admin.status === \"suspended\" ? \"text-red-600\" : admin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                                    children: admin.status === \"approved\" ? \"نشط\" : admin.status === \"suspended\" ? \"معلق\" : admin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: ((_admin_profile3 = admin.profile) === null || _admin_profile3 === void 0 ? void 0 : _admin_profile3.department) || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: formatTimestamp(admin.createdAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                        children: admin.lastLogin ? formatTimestamp(admin.lastLogin) : \"لم يسجل دخول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1\",\n                                                            children: [\n                                                                admin.permissions.slice(0, 2).map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: permission.replace(\"_\", \" \")\n                                                                    }, permission, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                admin.permissions.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"+\",\n                                                                        admin.permissions.length - 2\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setSelectedAdmin(admin),\n                                                                    className: \"text-blue-600 hover:text-blue-800\",\n                                                                    title: \"عرض التفاصيل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"تعديل\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                admin.status === \"approved\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-yellow-600 hover:text-yellow-800\",\n                                                                    title: \"تعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-green-600 hover:text-green-800\",\n                                                                    title: \"إلغاء التعليق\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                admin.role !== \"super_admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-red-600 hover:text-red-800\",\n                                                                    title: \"حذف\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, admin.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this),\n                            filteredAdmins.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"لا توجد مدراء تطابق المعايير المحددة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this),\n            showCreateModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_superadmin_SuperadminCreateAdminModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showCreateModal,\n                onClose: ()=>setShowCreateModal(false),\n                onSuccess: ()=>{\n                    setShowCreateModal(false);\n                    fetchAdmins() // Refresh the admin list\n                    ;\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 492,\n                columnNumber: 9\n            }, this),\n            selectedAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600 ml-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"تفاصيل المدير\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedAdmin(null),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(X, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الاسم الكامل\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile = selectedAdmin.profile) === null || _selectedAdmin_profile === void 0 ? void 0 : _selectedAdmin_profile.fullName) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"البريد الإلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.email\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"رقم الهاتف\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile1 = selectedAdmin.profile) === null || _selectedAdmin_profile1 === void 0 ? void 0 : _selectedAdmin_profile1.phone) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"القسم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Building, {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: ((_selectedAdmin_profile2 = selectedAdmin.profile) === null || _selectedAdmin_profile2 === void 0 ? void 0 : _selectedAdmin_profile2.department) || \"غير محدد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getRoleIcon(selectedAdmin.role),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.role === \"super_admin\" ? \"text-purple-600\" : \"text-blue-600\"),\n                                                            children: selectedAdmin.role === \"super_admin\" ? \"مدير عام\" : \"مدير\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        getStatusIcon(selectedAdmin.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 font-medium \".concat(selectedAdmin.status === \"approved\" ? \"text-green-600\" : selectedAdmin.status === \"suspended\" ? \"text-red-600\" : selectedAdmin.status === \"blocked\" ? \"text-red-700\" : \"text-yellow-600\"),\n                                                            children: selectedAdmin.status === \"approved\" ? \"نشط\" : selectedAdmin.status === \"suspended\" ? \"معلق\" : selectedAdmin.status === \"blocked\" ? \"محظور\" : \"في الانتظار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: formatTimestamp(selectedAdmin.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Crown_Edit_Eye_Lock_Mail_Phone_Search_Shield_Trash2_Unlock_UserPlus_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedAdmin.lastLogin ? formatTimestamp(selectedAdmin.lastLogin) : \"لم يسجل دخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                            children: \"الصلاحيات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                selectedAdmin.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                                        children: permission.replace(\"_\", \" \")\n                                                    }, permission, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                selectedAdmin.permissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"لا توجد صلاحيات محددة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, this),\n                                selectedAdmin.createdBy && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"تم الإنشاء بواسطة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center p-3 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                    className: \"h-4 w-4 text-gray-400 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: selectedAdmin.createdBy\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end p-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedAdmin(null),\n                                className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                children: \"إغلاق\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/superadmin/admin-management/page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(SuperAdminManagementPage, \"pXtJVnx1C6ShqQObK1fRUNxoyGI=\", false, function() {\n    return [\n        _hooks_useAdminAuth__WEBPACK_IMPORTED_MODULE_2__.useAdminAuth\n    ];\n});\n_c = SuperAdminManagementPage;\nvar _c;\n$RefreshReg$(_c, \"SuperAdminManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/superadmin/admin-management/page.tsx\n"));

/***/ })

});