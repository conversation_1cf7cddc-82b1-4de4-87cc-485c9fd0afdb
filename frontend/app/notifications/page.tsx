'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  Bell, 
  BellRing,
  Check,
  CheckCheck,
  Trash2,
  RefreshCw,
  Settings,
  Filter,
  Calendar,
  Clock,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Gavel,
  FileText,
  Users,
  DollarSign,
  Eye,
  MoreHorizontal
} from 'lucide-react'

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  data?: any
  read: boolean
  createdAt: string
  priority: 'low' | 'medium' | 'high'
  category: string
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')
  const [user, setUser] = useState<any>(null)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
    loadNotifications()
  }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      const response = await api.get('/notifications')
      
      if (response.data.success) {
        setNotifications(response.data.data.notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
      // Show empty notifications when API fails - no mock data
      setNotifications([])
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch(`/notifications/${notificationId}/read`)
      setNotifications(notifications.map(notif => 
        notif._id === notificationId ? { ...notif, read: true } : notif
      ))
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحديث الإشعار',
        variant: 'destructive'
      })
    }
  }

  const markAllAsRead = async () => {
    try {
      await api.patch('/notifications/mark-all-read')
      setNotifications(notifications.map(notif => ({ ...notif, read: true })))
      toast({
        title: 'تم التحديث',
        description: 'تم تحديد جميع الإشعارات كمقروءة'
      })
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحديث الإشعارات',
        variant: 'destructive'
      })
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      await api.delete(`/notifications/${notificationId}`)
      setNotifications(notifications.filter(notif => notif._id !== notificationId))
      toast({
        title: 'تم الحذف',
        description: 'تم حذف الإشعار بنجاح'
      })
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في حذف الإشعار',
        variant: 'destructive'
      })
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification._id)
    }

    // Navigate based on notification type
    if (notification.data) {
      switch (notification.type) {
        case 'new_bid':
        case 'auction_ending':
          router.push(`/auctions/${notification.data.auctionId}`)
          break
        case 'application_approved':
        case 'new_tender':
          router.push(`/tenders/${notification.data.tenderId}`)
          break
        default:
          break
      }
    }
  }

  const formatRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_bid':
        return <Gavel className="h-5 w-5 text-blue-600" />
      case 'auction_ending':
        return <Clock className="h-5 w-5 text-orange-600" />
      case 'application_approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'application_rejected':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'payment_received':
        return <DollarSign className="h-5 w-5 text-green-600" />
      case 'new_tender':
        return <FileText className="h-5 w-5 text-purple-600" />
      default:
        return <Bell className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">عالية</Badge>
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">متوسطة</Badge>
      case 'low':
        return <Badge variant="secondary">منخفضة</Badge>
      default:
        return null
    }
  }

  const filteredNotifications = notifications.filter(notif => {
    if (activeTab === 'all') return true
    if (activeTab === 'unread') return !notif.read
    if (activeTab === 'read') return notif.read
    return notif.category === activeTab
  })

  const unreadCount = notifications.filter(notif => !notif.read).length

  return (
    <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">الإشعارات</h1>
              <p className="text-blue-100 mt-1">
                متابعة جميع التحديثات والأنشطة المهمة
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{unreadCount}</div>
                <div className="text-sm text-blue-100">غير مقروءة</div>
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={loadNotifications}
                  variant="outline"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  <RefreshCw className="h-4 w-4 ml-2" />
                  تحديث
                </Button>
                {unreadCount > 0 && (
                  <Button 
                    onClick={markAllAsRead}
                    className="bg-white text-blue-600 hover:bg-gray-100"
                  >
                    <CheckCheck className="h-4 w-4 ml-2" />
                    تحديد الكل كمقروء
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tabs and Notifications */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">الكل ({notifications.length})</TabsTrigger>
            <TabsTrigger value="unread">غير مقروءة ({unreadCount})</TabsTrigger>
            <TabsTrigger value="read">مقروءة ({notifications.length - unreadCount})</TabsTrigger>
            <TabsTrigger value="auction">مزادات</TabsTrigger>
            <TabsTrigger value="tender">مناقصات</TabsTrigger>
            <TabsTrigger value="payment">مدفوعات</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <p className="text-gray-600">جاري تحميل الإشعارات...</p>
                </div>
              </div>
            ) : filteredNotifications.length > 0 ? (
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <Card 
                    key={notification._id} 
                    className={`transition-all hover:shadow-md cursor-pointer ${
                      !notification.read ? 'border-l-4 border-l-blue-500 bg-blue-50/50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="mt-1">
                            {getNotificationIcon(notification.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}>
                                {notification.title}
                              </h3>
                              {!notification.read && (
                                <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                              )}
                              {getPriorityBadge(notification.priority)}
                            </div>
                            <p className="text-gray-600 text-sm mb-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatRelativeTime(notification.createdAt)}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation()
                                markAsRead(notification._id)
                              }}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteNotification(notification._id)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إشعارات</h3>
                <p className="text-gray-600">
                  {activeTab === 'unread' 
                    ? 'جميع الإشعارات مقروءة' 
                    : 'ستظهر هنا الإشعارات الجديدة'
                  }
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
