'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const router = useRouter()

  useEffect(() => {
    // Get user data from localStorage
    const userStr = localStorage.getItem('user')
    if (!userStr) {
      router.push('/auth/login')
      return
    }

    try {
      const user = JSON.parse(userStr)
      const { role, status } = user

      // Redirect based on user role and status
      if (role === 'admin') {
        router.push('/admin/dashboard')
      } else if (role === 'super_admin') {
        router.push('/superadmin/dashboard')
      } else if (role === 'government') {
        router.push('/government/dashboard')
      } else if (role === 'company') {
        router.push('/company/dashboard')
      } else if (role === 'individual') {
        router.push('/user/dashboard')
      } else {
        // Default fallback
        router.push('/user/dashboard')
      }
    } catch (error) {
      console.error('Error parsing user data:', error)
      router.push('/auth/login')
    }
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p>جاري التحويل...</p>
      </div>
    </div>
  )
}
