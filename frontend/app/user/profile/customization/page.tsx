'use client'

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import DashboardLayout from '@/components/DashboardLayout';
import { Edit, Save, X } from 'lucide-react';

export default function EnhancedUserProfile() {
  const [user, setUser] = useState({
    profilePicture: '',
    fullName: '',
    phone: '',
    address: '',
    bio: '',
  });
  const [isEditing, setIsEditing] = useState(false);
  const [tempProfile, setTempProfile] = useState(user);

  useEffect(() => {
    // Load real user data from API - no mock data
    const loadUserProfile = async () => {
      try {
        const response = await fetch('/api/users/profile');
        const data = await response.json();

        if (data.success) {
          setUser(data.data.profile);
          setTempProfile(data.data.profile);
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
        // Show empty profile when API fails
        const emptyProfile = {
          profilePicture: '',
          fullName: '',
          phone: '',
          address: '',
          bio: '',
        };
        setUser(emptyProfile);
        setTempProfile(emptyProfile);
      }
    };

    loadUserProfile();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setTempProfile({ ...tempProfile, [name]: value });
  };

  const handleSave = () => {
    setUser(tempProfile);
    setIsEditing(false);
    // Implement save logic (e.g., API call)
  };

  const handleCancel = () => {
    setTempProfile(user);
    setIsEditing(false);
  };

  return (
    <DashboardLayout allowedRoles={['individual', 'company', 'admin', 'government']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">الملف الشخصي</h1>
            <p className="text-muted-foreground">تخصيص معلومات حسابك</p>
          </div>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="w-4 h-4 mr-2" /> تعديل
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" /> حفظ
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                <X className="w-4 h-4 mr-2" /> إلغاء
              </Button>
            </div>
          )}
        </header>

        <Card>
          <CardHeader>
            <CardTitle>معلومات الملف الشخصي</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex gap-4 items-center">
              <Avatar>
                <AvatarImage src={user.profilePicture || 'https://via.placeholder.com/150'} />
                <AvatarFallback>{user.fullName ? user.fullName[0] : 'A'}</AvatarFallback>
              </Avatar>
              {isEditing && (
                <Input
                  id="profilePicture"
                  name="profilePicture"
                  placeholder="رابط صورة الملف الشخصي"
                  value={tempProfile.profilePicture}
                  onChange={handleInputChange}
                  className="flex-1"
                />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">الاسم الكامل</Label>
                {isEditing ? (
                  <Input
                    id="fullName"
                    name="fullName"
                    value={tempProfile.fullName}
                    onChange={handleInputChange}
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">{user.fullName}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">رقم الهاتف</Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    name="phone"
                    value={tempProfile.phone}
                    onChange={handleInputChange}
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">{user.phone}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">العنوان</Label>
                {isEditing ? (
                  <Input
                    id="address"
                    name="address"
                    value={tempProfile.address}
                    onChange={handleInputChange}
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">{user.address}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="bio">السيرة الذاتية</Label>
                {isEditing ? (
                  <textarea
                    id="bio"
                    name="bio"
                    value={tempProfile.bio}
                    onChange={handleInputChange}
                    className="p-2 w-full h-24 bg-gray-50 rounded border"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border space-y-2">{user.bio}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
