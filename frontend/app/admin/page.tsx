'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  Gavel, 
  FileText, 
  Settings, 
  BarChart3, 
  Shield,
  ArrowRight,
  Crown,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface AdminStats {
  totalUsers: number
  activeAuctions: number
  activeTenders: number
  pendingApprovals: number
}

export default function AdminPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'admin' })
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeAuctions: 0,
    activeTenders: 0,
    pendingApprovals: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isAuthorized) {
      fetchStats()
    }
  }, [isAuthorized])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')

      const response = await fetch('http://localhost:5000/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        setStats({
          totalUsers: result.data.stats.totalUsers || 0,
          activeAuctions: result.data.stats.activeAuctions || 0,
          activeTenders: result.data.stats.activeTenders || 0,
          pendingApprovals: result.data.stats.pendingApprovals || 0
        })
      } else {
        // API call failed - show zeros instead of fake data
        console.error('API call failed, showing empty stats')
        setStats({
          totalUsers: 0,
          activeAuctions: 0,
          activeTenders: 0,
          pendingApprovals: 0
        })
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
      // Show zeros instead of fake data
      setStats({
        totalUsers: 0,
        activeAuctions: 0,
        activeTenders: 0,
        pendingApprovals: 0
      })
    } finally {
      setLoading(false)
    }
  }

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-red-200 shadow-xl">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-800">غير مصرح بالوصول</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-6">
              هذه الصفحة مخصصة للمديرين فقط. يرجى تسجيل الدخول بحساب إداري.
            </p>
            <Button 
              onClick={() => router.push('/auth/login')}
              className="w-full bg-red-600 hover:bg-red-700"
            >
              تسجيل الدخول
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const adminFeatures = [
    {
      title: 'لوحة التحكم',
      description: 'عرض الإحصائيات والتحليلات الشاملة',
      icon: BarChart3,
      href: '/admin/dashboard',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600'
    },
    {
      title: 'إدارة المستخدمين',
      description: 'إدارة حسابات المستخدمين والموافقات',
      icon: Users,
      href: '/admin/users',
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600'
    },
    {
      title: 'الحسابات المعلقة',
      description: 'مراجعة والموافقة على الحسابات الجديدة',
      icon: CheckCircle,
      href: '/admin/pending-accounts',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600'
    },
    {
      title: 'إدارة المزادات',
      description: 'مراقبة وإدارة جميع المزادات',
      icon: Gavel,
      href: '/admin/auctions',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-600'
    },
    {
      title: 'إدارة المناقصات',
      description: 'مراقبة وإدارة المناقصات الحكومية',
      icon: FileText,
      href: '/admin/tenders',
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      iconColor: 'text-indigo-600'
    },
    {
      title: 'التقارير والتحليلات',
      description: 'تقارير مفصلة وتحليلات متقدمة',
      icon: BarChart3,
      href: '/admin/reports',
      color: 'from-teal-500 to-teal-600',
      bgColor: 'bg-teal-50',
      iconColor: 'text-teal-600'
    },
    {
      title: 'إعدادات النظام',
      description: 'إعدادات المنصة والتكوينات',
      icon: Settings,
      href: '/admin/settings',
      color: 'from-gray-500 to-gray-600',
      bgColor: 'bg-gray-50',
      iconColor: 'text-gray-600'
    },
    {
      title: 'قوالب البريد الإلكتروني',
      description: 'إدارة قوالب الرسائل الإلكترونية',
      icon: AlertCircle,
      href: '/admin/email-templates',
      color: 'from-pink-500 to-pink-600',
      bgColor: 'bg-pink-50',
      iconColor: 'text-pink-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                لوحة التحكم الإدارية
              </h1>
              <p className="text-gray-600">
                مرحباً {user?.profile?.firstName} {user?.profile?.lastName} - إدارة شاملة للمنصة
              </p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">المستخدمين النشطين</p>
                  <p className="text-2xl font-bold">{loading ? '...' : stats.totalUsers.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-green-500 to-green-600 text-white shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">المزادات النشطة</p>
                  <p className="text-2xl font-bold">{loading ? '...' : stats.activeAuctions}</p>
                </div>
                <Gavel className="w-8 h-8 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">المناقصات المفتوحة</p>
                  <p className="text-2xl font-bold">{loading ? '...' : stats.activeTenders}</p>
                </div>
                <FileText className="w-8 h-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-orange-500 to-orange-600 text-white shadow-xl">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">الحسابات المعلقة</p>
                  <p className="text-2xl font-bold">{loading ? '...' : stats.pendingApprovals}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admin Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {adminFeatures.map((feature, index) => (
            <Card 
              key={index}
              className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 cursor-pointer group"
              onClick={() => router.push(feature.href)}
            >
              <CardContent className="p-6">
                <div className={`w-12 h-12 ${feature.bgColor} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className={`w-6 h-6 ${feature.iconColor}`} />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {feature.description}
                </p>
                <div className="flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700">
                  <span>الانتقال</span>
                  <ArrowRight className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-gray-900">
                إجراءات سريعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={() => router.push('/admin/dashboard')}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white h-12"
                >
                  <BarChart3 className="w-5 h-5 ml-2" />
                  عرض لوحة التحكم
                </Button>
                <Button 
                  onClick={() => router.push('/admin/pending-accounts')}
                  className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white h-12"
                >
                  <CheckCircle className="w-5 h-5 ml-2" />
                  مراجعة الحسابات المعلقة
                </Button>
                <Button 
                  onClick={() => router.push('/admin/reports')}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white h-12"
                >
                  <FileText className="w-5 h-5 ml-2" />
                  إنشاء تقرير
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
