'use client'

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { adminAPI } from '@/lib/api';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  BarChart3,
  Users,
  Gavel,
  FileText,
  TrendingUp,
  Activity,
  Clock,
  ArrowUpRight,
  Calendar,
  Target,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

export default function AdminDashboard() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'admin' });
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (!isAuthorized) {
      return;
    }

    const fetchDashboardData = async () => {
      try {
        const dashboardResponse = await adminAPI.getDashboardStats();
        setDashboardData(dashboardResponse.data.data);
      } catch (err) {
        console.error('Dashboard error:', err);
        setError('خطأ في تحميل بيانات لوحة التحكم');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthorized]);

  const handleCardClick = (path: string) => {
    router.push(path);
  };

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center mx-auto animate-pulse">
            <BarChart3 className="w-8 h-8 text-white" />
          </div>
          <div className="space-y-2">
            <p className="text-lg font-semibold text-gray-700">جاري تحميل لوحة التحكم...</p>
            <p className="text-sm text-gray-500">يتم تحضير البيانات والإحصائيات</p>
          </div>
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto">
            <Activity className="w-8 h-8 text-white" />
          </div>
          <div className="space-y-2">
            <p className="text-lg font-semibold text-red-700">خطأ: {error}</p>
            <p className="text-sm text-gray-500">يرجى المحاولة مرة أخرى</p>
          </div>
        </div>
      </div>
    );
  }

  const stats = dashboardData?.stats || {};

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <header role="banner" className="text-center">
        <div className="inline-flex items-center gap-4 px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-200/30 backdrop-blur-sm mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
            <BarChart3 className="w-5 h-5 text-white" />
          </div>
          <div className="text-right">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              لوحة تحكم المدير
            </h1>
            <p className="text-sm text-gray-600 font-medium">إدارة المستخدمين والمحتوى</p>
          </div>
        </div>
      </header>

      <main role="main" className="space-y-6">
        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Users Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/admin/users')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    إجمالي المستخدمين
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.totalUsers?.toLocaleString() || '0'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">+12%</span>
                    <span className="text-gray-500 mr-1">هذا الشهر</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>
          
          {/* Pending Approvals Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-orange-50/30 to-amber-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/admin/pending-accounts')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    الموافقات المعلقة
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.pendingApprovals?.toLocaleString() || '0'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <Clock className="w-3 h-3 text-orange-500 mr-1" />
                    <span className="text-orange-600 font-medium">يتطلب مراجعة</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Clock className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Active Auctions Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-green-50/30 to-emerald-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/admin/auctions')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    المزادات النشطة
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.activeAuctions?.toLocaleString() || '0'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">نشطة الآن</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Gavel className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Active Tenders Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-purple-50/30 to-violet-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/admin/tenders')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-violet-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    المناقصات النشطة
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.activeTenders?.toLocaleString() || '0'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <Target className="w-3 h-3 text-purple-500 mr-1" />
                    <span className="text-purple-600 font-medium">مفتوحة للعروض</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <FileText className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                المهام السريعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <button 
                  onClick={() => handleCardClick('/admin/pending-accounts')}
                  className="w-full text-right p-3 rounded-lg bg-white/60 hover:bg-white/80 transition-colors border border-white/40"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">مراجعة الحسابات المعلقة</span>
                    <AlertTriangle className="w-4 h-4 text-orange-500" />
                  </div>
                </button>
                <button 
                  onClick={() => handleCardClick('/admin/users')}
                  className="w-full text-right p-3 rounded-lg bg-white/60 hover:bg-white/80 transition-colors border border-white/40"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">إدارة المستخدمين</span>
                    <Users className="w-4 h-4 text-blue-500" />
                  </div>
                </button>
                <button 
                  onClick={() => handleCardClick('/admin/auctions')}
                  className="w-full text-right p-3 rounded-lg bg-white/60 hover:bg-white/80 transition-colors border border-white/40"
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">مراقبة المزادات</span>
                    <Gavel className="w-4 h-4 text-green-500" />
                  </div>
                </button>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-white via-green-50/30 to-emerald-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                النشاط الحديث
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 rounded-lg bg-white/60 border border-white/40">
                  <div className="text-sm text-gray-600">مستخدم جديد سجل</div>
                  <div className="text-xs text-gray-500">منذ 5 دقائق</div>
                </div>
                <div className="p-3 rounded-lg bg-white/60 border border-white/40">
                  <div className="text-sm text-gray-600">مزاد جديد تم إنشاؤه</div>
                  <div className="text-xs text-gray-500">منذ 15 دقيقة</div>
                </div>
                <div className="p-3 rounded-lg bg-white/60 border border-white/40">
                  <div className="text-sm text-gray-600">مناقصة تم إغلاقها</div>
                  <div className="text-xs text-gray-500">منذ ساعة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-white via-purple-50/30 to-violet-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <Calendar className="w-5 h-5 text-purple-600" />
                إحصائيات اليوم
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">مستخدمين جدد</span>
                  <span className="text-lg font-bold text-blue-600">12</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">مزادات نشطة</span>
                  <span className="text-lg font-bold text-green-600">8</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">مناقصات جديدة</span>
                  <span className="text-lg font-bold text-purple-600">3</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
