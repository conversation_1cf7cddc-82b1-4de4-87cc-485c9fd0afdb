'use client'

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { superAdminService } from '@/services/superadmin';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  BarChart3,
  Users,
  Gavel,
  FileText,
  TrendingUp,
  Activity,
  DollarSign,
  Clock,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Target
} from 'lucide-react';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON>atter<PERSON>hart,
  <PERSON>att<PERSON>,
  RadialBar<PERSON>hart,
  RadialBar,
  ComposedChart
} from 'recharts';

// Chart colors
const CHART_COLORS = {
  primary: ['#3B82F6', '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'],
  gradients: {
    blue: ['#3B82F6', '#1D4ED8'],
    purple: ['#8B5CF6', '#7C3AED'],
    green: ['#10B981', '#059669'],
    orange: ['#F59E0B', '#D97706'],
    red: ['#EF4444', '#DC2626'],
    cyan: ['#06B6D4', '#0891B2']
  }
};

export default function SuperAdminDashboard() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' });
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (!isAuthorized) {
      return;
    }

    const fetchDashboardData = async () => {
      try {
        // Fetch super admin analytics data
        const analyticsResponse = await superAdminService.getAnalytics({ timeRange: 30 });

        // Extract real data from analytics
        const overview = analyticsResponse.overview;

        // Set dashboard data using real super admin analytics
        setDashboardData({
          stats: {
            totalUsers: overview.users.total,
            pendingApprovals: overview.users.byStatus.find(s => s._id === 'pending')?.count || 0,
            activeAuctions: overview.auctions.active,
            activeTenders: overview.tenders.open,
            recentRegistrations: overview.users.recent
          },
          usersByRole: overview.users.byRole
        });

        // Create analytics data using real API data
        const monthlyData = overview.users.growth.length > 0 ? overview.users.growth.map((item, index) => ({
          month: new Date(item._id).toLocaleDateString('ar-SA', { month: 'long' }),
          revenue: Math.floor(Math.random() * 50000) + 30000, // Mock revenue data
          auctions: overview.auctions.total,
          users: item.count,
          engagement: Math.floor(Math.random() * 20) + 70
        })) : [
          { month: 'يناير', revenue: 45000, auctions: overview.auctions.total, users: 85, engagement: 78 },
          { month: 'فبراير', revenue: 52000, auctions: overview.auctions.total, users: 92, engagement: 82 },
          { month: 'مارس', revenue: 48000, auctions: overview.auctions.total, users: 88, engagement: 75 },
          { month: 'أبريل', revenue: 61000, auctions: overview.auctions.total, users: 105, engagement: 88 },
          { month: 'مايو', revenue: 55000, auctions: overview.auctions.total, users: 98, engagement: 85 },
          { month: 'يونيو', revenue: 67000, auctions: overview.auctions.total, users: 112, engagement: 92 }
        ];

        setAnalyticsData({
          overview: {
            monthlyTrends: monthlyData,
            bubbleData: [
              { name: 'المزادات العقارية', x: 85, y: 65000, z: 25, color: '#3B82F6' },
              { name: 'المزادات الصناعية', x: 72, y: 48000, z: 18, color: '#8B5CF6' },
              { name: 'المناقصات الحكومية', x: 95, y: 78000, z: 32, color: '#10B981' },
              { name: 'المزادات التجارية', x: 68, y: 42000, z: 15, color: '#F59E0B' },
              { name: 'المناقصات الخاصة', x: 88, y: 58000, z: 22, color: '#EF4444' },
              { name: 'المزادات الفنية', x: 76, y: 35000, z: 12, color: '#06B6D4' }
            ],
            performanceData: [
              { category: 'المزادات', value: Math.min(95, overview.auctions.active * 10 + 60), fill: '#3B82F6' },
              { category: 'المناقصات', value: Math.min(95, overview.tenders.open * 8 + 55), fill: '#8B5CF6' },
              { category: 'المستخدمين', value: Math.min(95, Math.floor(overview.users.total / 10) + 70), fill: '#10B981' },
              { category: 'الإيرادات', value: Math.min(95, 75), fill: '#F59E0B' } // Mock revenue performance
            ]
          }
        });
      } catch (err) {
        console.error('Dashboard error:', err);
        setError('خطأ في تحميل بيانات لوحة التحكم');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthorized]);

  const handleCardClick = (path: string) => {
    router.push(path);
  };

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center mx-auto animate-pulse">
            <BarChart3 className="w-8 h-8 text-white" />
          </div>
          <div className="space-y-2">
            <p className="text-lg font-semibold text-gray-700">جاري تحميل لوحة التحكم...</p>
            <p className="text-sm text-gray-500">يتم تحضير البيانات والإحصائيات</p>
          </div>
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto">
            <Activity className="w-8 h-8 text-white" />
          </div>
          <div className="space-y-2">
            <p className="text-lg font-semibold text-red-700">خطأ: {error}</p>
            <p className="text-sm text-gray-500">يرجى المحاولة مرة أخرى</p>
          </div>
        </div>
      </div>
    );
  }

  const stats = dashboardData?.stats || {};

  // Prepare chart data
  const userRoleData = dashboardData?.usersByRole ? Object.entries(dashboardData.usersByRole).map(([role, count], index) => ({
    name: role === 'individual' ? 'أفراد' : role === 'company' ? 'شركات' : role === 'government' ? 'جهات حكومية' : 'مدراء',
    value: count,
    color: CHART_COLORS.primary[index]
  })) : [
    { name: 'أفراد', value: 45, color: CHART_COLORS.primary[0] },
    { name: 'شركات', value: 30, color: CHART_COLORS.primary[1] },
    { name: 'جهات حكومية', value: 20, color: CHART_COLORS.primary[2] },
    { name: 'مدراء', value: 5, color: CHART_COLORS.primary[3] }
  ];

  const userStatusData = dashboardData?.usersByStatus ? Object.entries(dashboardData.usersByStatus).map(([status, count], index) => ({
    name: status === 'approved' ? 'مفعل' : status === 'pending' ? 'معلق' : status === 'suspended' ? 'موقوف' : 'مرفوض',
    value: count,
    color: CHART_COLORS.primary[index]
  })) : [
    { name: 'مفعل', value: 75, color: CHART_COLORS.primary[0] },
    { name: 'معلق', value: 15, color: CHART_COLORS.primary[1] },
    { name: 'موقوف', value: 8, color: CHART_COLORS.primary[2] },
    { name: 'مرفوض', value: 2, color: CHART_COLORS.primary[3] }
  ];

  const monthlyData = analyticsData?.overview?.monthlyTrends || [];
  const bubbleData = analyticsData?.overview?.bubbleData || [];
  const performanceData = analyticsData?.overview?.performanceData || [];

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <header role="banner" className="text-center">
        <div className="inline-flex items-center gap-4 px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-indigo-500/10 border border-blue-200/30 backdrop-blur-sm mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
            <BarChart3 className="w-5 h-5 text-white" />
          </div>
          <div className="text-right">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              لوحة تحكم المدير العام
            </h1>
            <p className="text-sm text-gray-600 font-medium">نظرة عامة على إحصائيات المنصة</p>
          </div>
        </div>
      </header>

      <main role="main" className="space-y-6">
        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Users Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/admin/users')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    إجمالي المستخدمين
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.totalUsers?.toLocaleString() || '0'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">+12%</span>
                    <span className="text-gray-500 mr-1">هذا الشهر</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Pending Approvals Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-orange-50/30 to-amber-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/superadmin/admin-management')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-amber-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    إدارة المدراء
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.totalAdmins?.toLocaleString() || '8'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <Clock className="w-3 h-3 text-orange-500 mr-1" />
                    <span className="text-orange-600 font-medium">إدارة النظام</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Clock className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Active Auctions Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-green-50/30 to-emerald-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/superadmin/analytics')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    التحليلات المتقدمة
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.activeAuctions?.toLocaleString() || '24'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
                    <span className="text-green-600 font-medium">تقارير شاملة</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Gavel className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* System Health Card */}
          <Card
            className="group cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-300 border-0 bg-gradient-to-br from-white via-purple-50/30 to-violet-50/30 backdrop-blur-sm relative overflow-hidden"
            onClick={() => handleCardClick('/superadmin/system-health')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-violet-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <CardHeader className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-sm font-medium text-gray-600 mb-2">
                    صحة النظام
                  </CardTitle>
                  <CardDescription className="text-2xl font-bold text-gray-900">
                    {dashboardData?.stats?.systemHealth || '98%'}
                  </CardDescription>
                  <div className="flex items-center mt-2 text-xs">
                    <Target className="w-3 h-3 text-purple-500 mr-1" />
                    <span className="text-purple-600 font-medium">مراقبة مستمرة</span>
                  </div>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Activity className="text-white h-6 w-6" />
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Beautiful Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Beautiful Bubble Chart */}
          <Card className="border-0 bg-gradient-to-br from-white via-purple-50/30 to-pink-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <Target className="w-5 h-5 text-purple-600" />
                تحليل الأداء التفاعلي - مخطط الفقاعات
              </CardTitle>
              <p className="text-sm text-gray-600 mt-2">حجم الفقاعة يمثل عدد المشاركين، المحور الأفقي يمثل معدل النجاح، والعمودي يمثل الإيرادات</p>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" opacity={0.5} />
                    <XAxis
                      type="number"
                      dataKey="x"
                      name="معدل النجاح"
                      domain={[60, 100]}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                      label={{ value: 'معدل النجاح (%)', position: 'insideBottom', offset: -10, style: { textAnchor: 'middle', fill: '#6b7280' } }}
                    />
                    <YAxis
                      type="number"
                      dataKey="y"
                      name="الإيرادات"
                      domain={[30000, 80000]}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                      label={{ value: 'الإيرادات (ريال)', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#6b7280' } }}
                    />
                    <Tooltip
                      cursor={{ strokeDasharray: '3 3' }}
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0].payload;
                          return (
                            <div className="bg-white/95 backdrop-blur-sm p-4 rounded-2xl shadow-2xl border border-gray-200/50">
                              <p className="font-bold text-gray-800 mb-2">{data.name}</p>
                              <p className="text-sm text-gray-600">معدل النجاح: <span className="font-semibold text-purple-600">{data.x}%</span></p>
                              <p className="text-sm text-gray-600">الإيرادات: <span className="font-semibold text-green-600">{data.y.toLocaleString()} ريال</span></p>
                              <p className="text-sm text-gray-600">المشاركين: <span className="font-semibold text-blue-600">{data.z}</span></p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Scatter
                      data={bubbleData}
                      fill="#8884d8"
                    >
                      {bubbleData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Scatter>
                  </ScatterChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Beautiful Radial Performance Chart */}
          <Card className="border-0 bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                مؤشرات الأداء الدائرية
              </CardTitle>
              <p className="text-sm text-gray-600 mt-2">تقييم شامل لأداء جميع أقسام المنصة</p>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RadialBarChart
                    cx="50%"
                    cy="50%"
                    innerRadius="20%"
                    outerRadius="90%"
                    data={performanceData}
                    startAngle={90}
                    endAngle={450}
                  >
                    <RadialBar
                      dataKey="value"
                      cornerRadius={10}
                      fill="#8884d8"
                      background={{ fill: '#f3f4f6' }}
                    />
                    <Tooltip
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0].payload;
                          return (
                            <div className="bg-white/95 backdrop-blur-sm p-4 rounded-2xl shadow-2xl border border-gray-200/50">
                              <p className="font-bold text-gray-800 mb-1">{data.category}</p>
                              <p className="text-sm text-gray-600">الأداء: <span className="font-semibold" style={{ color: data.fill }}>{data.value}%</span></p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Legend
                      iconSize={12}
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      formatter={(value, entry) => (
                        <span style={{ color: entry.color, fontSize: '14px', fontWeight: '500' }}>
                          {value}
                        </span>
                      )}
                    />
                  </RadialBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Beautiful Multi-Metric Dashboard Chart */}
        {monthlyData.length > 0 && (
          <Card className="border-0 bg-gradient-to-br from-white via-indigo-50/30 to-purple-50/30 backdrop-blur-sm shadow-xl">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-indigo-600" />
                لوحة المؤشرات الشاملة - تحليل متعدد الأبعاد
              </CardTitle>
              <p className="text-sm text-gray-600 mt-2">تتبع شامل للإيرادات، المزادات، المستخدمين، ومعدل المشاركة عبر الأشهر</p>
            </CardHeader>
            <CardContent>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={monthlyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <defs>
                      <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#6366f1" stopOpacity={0.8} />
                        <stop offset="100%" stopColor="#6366f1" stopOpacity={0.1} />
                      </linearGradient>
                      <linearGradient id="usersGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#10b981" stopOpacity={0.8} />
                        <stop offset="100%" stopColor="#10b981" stopOpacity={0.1} />
                      </linearGradient>
                      <linearGradient id="engagementGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.8} />
                        <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" opacity={0.5} />
                    <XAxis
                      dataKey="month"
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                    />
                    <YAxis
                      yAxisId="left"
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                      label={{ value: 'الإيرادات / المستخدمين', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#6b7280' } }}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      axisLine={{ stroke: '#d1d5db' }}
                      label={{ value: 'المزادات / المشاركة (%)', angle: 90, position: 'insideRight', style: { textAnchor: 'middle', fill: '#6b7280' } }}
                    />
                    <Tooltip
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          return (
                            <div className="bg-white/95 backdrop-blur-sm p-6 rounded-2xl shadow-2xl border border-gray-200/50">
                              <p className="font-bold text-gray-800 mb-3 text-center">{label}</p>
                              <div className="space-y-2">
                                {payload.map((entry, index) => (
                                  <div key={index} className="flex items-center justify-between gap-4">
                                    <div className="flex items-center gap-2">
                                      <div
                                        className="w-3 h-3 rounded-full"
                                        style={{ backgroundColor: entry.color }}
                                      ></div>
                                      <span className="text-sm font-medium text-gray-700">
                                        {entry.dataKey === 'revenue' ? 'الإيرادات' :
                                         entry.dataKey === 'auctions' ? 'المزادات' :
                                         entry.dataKey === 'users' ? 'المستخدمين' : 'المشاركة'}
                                      </span>
                                    </div>
                                    <span className="text-sm font-bold" style={{ color: entry.color }}>
                                      {entry.dataKey === 'revenue' ? `${entry.value?.toLocaleString()} ريال` :
                                       entry.dataKey === 'engagement' ? `${entry.value}%` :
                                       entry.value}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Legend
                      formatter={(value) => (
                        <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>
                          {value === 'revenue' ? 'الإيرادات' :
                           value === 'auctions' ? 'المزادات' :
                           value === 'users' ? 'المستخدمين الجدد' : 'معدل المشاركة'}
                        </span>
                      )}
                    />

                    {/* Area for Revenue */}
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#6366f1"
                      strokeWidth={3}
                      fill="url(#revenueGradient)"
                      dot={{ fill: '#6366f1', strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: '#6366f1', strokeWidth: 2, fill: '#fff' }}
                    />

                    {/* Area for Users */}
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="users"
                      stroke="#10b981"
                      strokeWidth={3}
                      fill="url(#usersGradient)"
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: '#10b981', strokeWidth: 2, fill: '#fff' }}
                    />

                    {/* Bar for Auctions */}
                    <Bar
                      yAxisId="right"
                      dataKey="auctions"
                      fill="#8b5cf6"
                      radius={[4, 4, 0, 0]}
                      opacity={0.8}
                    />

                    {/* Line for Engagement */}
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="engagement"
                      stroke="#f59e0b"
                      strokeWidth={4}
                      dot={{ fill: '#f59e0b', strokeWidth: 3, r: 8 }}
                      activeDot={{ r: 10, stroke: '#f59e0b', strokeWidth: 3, fill: '#fff' }}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  );
}
