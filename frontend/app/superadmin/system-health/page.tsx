'use client'

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { 
  Activity, 
  Server, 
  Database, 
  Cpu, 
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Zap,
  Globe,
  Mail,
  CreditCard,
  RefreshCw,
  Download,
  Bell,
  Eye,
  BarChart3,
  Monitor,
  Thermometer
} from 'lucide-react'

interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical'
  uptime: number
  lastUpdated: string
  services: {
    api: ServiceStatus
    database: ServiceStatus
    cache: ServiceStatus
    email: ServiceStatus
    payment: ServiceStatus
    storage: ServiceStatus
  }
  performance: {
    cpu: PerformanceMetric
    memory: PerformanceMetric
    disk: PerformanceMetric
    network: PerformanceMetric
  }
  alerts: SystemAlert[]
}

interface ServiceStatus {
  status: 'online' | 'offline' | 'degraded'
  responseTime: number
  uptime: number
  lastCheck: string
  errorRate: number
}

interface PerformanceMetric {
  current: number
  average: number
  peak: number
  trend: 'up' | 'down' | 'stable'
  history: { timestamp: string; value: number }[]
}

interface SystemAlert {
  id: string
  type: 'critical' | 'warning' | 'info'
  title: string
  description: string
  timestamp: string
  resolved: boolean
  service?: string
}

export default function SuperAdminSystemHealthPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' })
  const [health, setHealth] = useState<SystemHealth | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(30) // seconds

  useEffect(() => {
    if (isAuthorized) {
      fetchSystemHealth()
    }
  }, [isAuthorized])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(() => {
        fetchSystemHealth()
      }, refreshInterval * 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  const fetchSystemHealth = async () => {
    try {
      setLoading(true)
      // Fetch real system health from API
      const response = await fetch('http://localhost:5000/api/superadmin/system-health', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setHealth(result.data)
          return
        }
      } else {
        console.error('Failed to fetch system health:', response.status)
      }

      // Fallback to mock health data if API fails
      const mockHealth: SystemHealth = {
        overall: 'healthy',
        uptime: 2592000, // 30 days in seconds
        lastUpdated: new Date().toISOString(),
        services: {
          api: {
            status: 'online',
            responseTime: 145,
            uptime: 99.9,
            lastCheck: new Date().toISOString(),
            errorRate: 0.1
          },
          database: {
            status: 'online',
            responseTime: 23,
            uptime: 99.8,
            lastCheck: new Date().toISOString(),
            errorRate: 0.2
          },
          cache: {
            status: 'online',
            responseTime: 5,
            uptime: 99.95,
            lastCheck: new Date().toISOString(),
            errorRate: 0.05
          },
          email: {
            status: 'degraded',
            responseTime: 2340,
            uptime: 98.5,
            lastCheck: new Date().toISOString(),
            errorRate: 1.5
          },
          payment: {
            status: 'online',
            responseTime: 890,
            uptime: 99.7,
            lastCheck: new Date().toISOString(),
            errorRate: 0.3
          },
          storage: {
            status: 'online',
            responseTime: 67,
            uptime: 99.9,
            lastCheck: new Date().toISOString(),
            errorRate: 0.1
          }
        },
        performance: {
          cpu: {
            current: 45.2,
            average: 38.7,
            peak: 89.3,
            trend: 'up',
            history: [
              { timestamp: '2025-01-12T10:00:00Z', value: 42.1 },
              { timestamp: '2025-01-12T10:05:00Z', value: 43.8 },
              { timestamp: '2025-01-12T10:10:00Z', value: 45.2 }
            ]
          },
          memory: {
            current: 67.8,
            average: 65.2,
            peak: 78.9,
            trend: 'stable',
            history: [
              { timestamp: '2025-01-12T10:00:00Z', value: 66.5 },
              { timestamp: '2025-01-12T10:05:00Z', value: 67.1 },
              { timestamp: '2025-01-12T10:10:00Z', value: 67.8 }
            ]
          },
          disk: {
            current: 34.5,
            average: 33.8,
            peak: 45.2,
            trend: 'up',
            history: [
              { timestamp: '2025-01-12T10:00:00Z', value: 33.2 },
              { timestamp: '2025-01-12T10:05:00Z', value: 33.9 },
              { timestamp: '2025-01-12T10:10:00Z', value: 34.5 }
            ]
          },
          network: {
            current: 12.3,
            average: 15.7,
            peak: 67.8,
            trend: 'down',
            history: [
              { timestamp: '2025-01-12T10:00:00Z', value: 15.4 },
              { timestamp: '2025-01-12T10:05:00Z', value: 13.8 },
              { timestamp: '2025-01-12T10:10:00Z', value: 12.3 }
            ]
          }
        },
        alerts: [
          {
            id: '1',
            type: 'warning',
            title: 'خدمة البريد الإلكتروني بطيئة',
            description: 'زمن الاستجابة لخدمة البريد الإلكتروني أعلى من المعتاد (2.3 ثانية)',
            timestamp: '2025-01-12T10:25:00Z',
            resolved: false,
            service: 'email'
          },
          {
            id: '2',
            type: 'info',
            title: 'تحديث النظام مكتمل',
            description: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.4',
            timestamp: '2025-01-12T09:15:00Z',
            resolved: true
          },
          {
            id: '3',
            type: 'critical',
            title: 'استخدام عالي للذاكرة',
            description: 'استخدام الذاكرة وصل إلى 89% - قد يؤثر على الأداء',
            timestamp: '2025-01-12T08:45:00Z',
            resolved: true
          }
        ]
      }
      
      setHealth(mockHealth)
    } catch (error) {
      console.error('Error fetching system health:', error)
      setError('خطأ في تحميل حالة النظام')
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'offline':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'degraded':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-100'
      case 'offline':
        return 'text-red-600 bg-red-100'
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-red-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-green-500" />
      default:
        return <Activity className="h-4 w-4 text-blue-500" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <CheckCircle className="h-5 w-5 text-blue-500" />
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}د ${hours}س ${minutes}ق`
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null
  }

  if (loading && !health) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل حالة النظام...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!health) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">خطأ في تحميل حالة النظام</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Activity className="h-8 w-8 text-blue-600 ml-3" />
              صحة النظام والمراقبة
            </h1>
            <p className="text-gray-600 mt-2">مراقبة الأداء وحالة الخدمات في الوقت الفعلي</p>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="text-sm text-gray-600">تحديث تلقائي:</span>
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoRefresh ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  autoRefresh ? 'translate-x-6' : 'translate-x-1'
                }`} />
              </button>
            </div>

            <button
              onClick={fetchSystemHealth}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>

            <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <Download className="h-4 w-4 ml-2" />
              تقرير الحالة
            </button>
          </div>
        </div>
      </div>

      {/* Overall Status */}
      <div className="mb-8">
        <div className={`rounded-lg p-6 border-2 ${
          health.overall === 'healthy' ? 'bg-green-50 border-green-200' :
          health.overall === 'warning' ? 'bg-yellow-50 border-yellow-200' :
          'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {health.overall === 'healthy' ?
                <CheckCircle className="h-8 w-8 text-green-500 ml-3" /> :
                health.overall === 'warning' ?
                <AlertTriangle className="h-8 w-8 text-yellow-500 ml-3" /> :
                <XCircle className="h-8 w-8 text-red-500 ml-3" />
              }
              <div>
                <h2 className={`text-2xl font-bold ${
                  health.overall === 'healthy' ? 'text-green-700' :
                  health.overall === 'warning' ? 'text-yellow-700' :
                  'text-red-700'
                }`}>
                  {health.overall === 'healthy' ? 'النظام يعمل بشكل طبيعي' :
                   health.overall === 'warning' ? 'النظام يعمل مع تحذيرات' :
                   'النظام يواجه مشاكل حرجة'}
                </h2>
                <p className="text-gray-600">
                  وقت التشغيل: {formatUptime(health.uptime)} | آخر تحديث: {formatTimestamp(health.lastUpdated)}
                </p>
              </div>
            </div>

            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">99.8%</div>
              <div className="text-sm text-gray-600">معدل التوفر</div>
            </div>
          </div>
        </div>
      </div>

      {/* Services Status */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">حالة الخدمات</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(health.services).map(([serviceName, service]) => (
            <div key={serviceName} className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  {serviceName === 'api' && <Server className="h-5 w-5 text-blue-500 ml-2" />}
                  {serviceName === 'database' && <Database className="h-5 w-5 text-purple-500 ml-2" />}
                  {serviceName === 'cache' && <Zap className="h-5 w-5 text-orange-500 ml-2" />}
                  {serviceName === 'email' && <Mail className="h-5 w-5 text-green-500 ml-2" />}
                  {serviceName === 'payment' && <CreditCard className="h-5 w-5 text-indigo-500 ml-2" />}
                  {serviceName === 'storage' && <HardDrive className="h-5 w-5 text-gray-500 ml-2" />}
                  <span className="font-medium text-gray-900">
                    {serviceName === 'api' ? 'API' :
                     serviceName === 'database' ? 'قاعدة البيانات' :
                     serviceName === 'cache' ? 'التخزين المؤقت' :
                     serviceName === 'email' ? 'البريد الإلكتروني' :
                     serviceName === 'payment' ? 'المدفوعات' :
                     'التخزين'}
                  </span>
                </div>
                {getStatusIcon(service.status)}
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">الحالة:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                    {service.status === 'online' ? 'متصل' :
                     service.status === 'offline' ? 'غير متصل' : 'بطيء'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">زمن الاستجابة:</span>
                  <span className="font-medium">{service.responseTime}ms</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">وقت التشغيل:</span>
                  <span className="font-medium">{service.uptime}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">معدل الأخطاء:</span>
                  <span className="font-medium">{service.errorRate}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">مؤشرات الأداء</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Object.entries(health.performance).map(([metricName, metric]) => (
            <div key={metricName} className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  {metricName === 'cpu' && <Cpu className="h-5 w-5 text-blue-500 ml-2" />}
                  {metricName === 'memory' && <Monitor className="h-5 w-5 text-green-500 ml-2" />}
                  {metricName === 'disk' && <HardDrive className="h-5 w-5 text-purple-500 ml-2" />}
                  {metricName === 'network' && <Wifi className="h-5 w-5 text-orange-500 ml-2" />}
                  <span className="font-medium text-gray-900">
                    {metricName === 'cpu' ? 'المعالج' :
                     metricName === 'memory' ? 'الذاكرة' :
                     metricName === 'disk' ? 'القرص الصلب' :
                     'الشبكة'}
                  </span>
                </div>
                {getTrendIcon(metric.trend)}
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">الحالي:</span>
                  <span className="font-bold text-lg">{metric.current}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">المتوسط:</span>
                  <span className="font-medium">{metric.average}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">الذروة:</span>
                  <span className="font-medium">{metric.peak}%</span>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                  <div
                    className={`h-2 rounded-full ${
                      metric.current > 80 ? 'bg-red-500' :
                      metric.current > 60 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${metric.current}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* System Alerts */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">تنبيهات النظام</h3>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-gray-900">
                التنبيهات الحديثة ({health.alerts.length})
              </h4>
              <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                عرض الكل
              </button>
            </div>
          </div>

          <div className="divide-y divide-gray-200">
            {health.alerts.slice(0, 5).map((alert) => (
              <div key={alert.id} className={`p-4 ${alert.resolved ? 'opacity-60' : ''}`}>
                <div className="flex items-start">
                  {getAlertIcon(alert.type)}
                  <div className="mr-3 flex-1">
                    <div className="flex items-center justify-between">
                      <h5 className="text-sm font-medium text-gray-900">{alert.title}</h5>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {alert.service && (
                          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                            {alert.service}
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(alert.timestamp)}
                        </span>
                        {alert.resolved && (
                          <span className="px-2 py-1 bg-green-100 text-green-600 text-xs rounded-full">
                            تم الحل
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {health.alerts.length === 0 && (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-gray-500">لا توجد تنبيهات حالياً</p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:bg-gray-50 transition-colors text-right">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">إعادة تشغيل الخدمات</h4>
              <p className="text-sm text-gray-600">إعادة تشغيل جميع الخدمات</p>
            </div>
            <RefreshCw className="h-6 w-6 text-blue-500" />
          </div>
        </button>

        <button className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:bg-gray-50 transition-colors text-right">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">تنظيف التخزين المؤقت</h4>
              <p className="text-sm text-gray-600">مسح جميع البيانات المؤقتة</p>
            </div>
            <Zap className="h-6 w-6 text-orange-500" />
          </div>
        </button>

        <button className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:bg-gray-50 transition-colors text-right">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">تحسين قاعدة البيانات</h4>
              <p className="text-sm text-gray-600">تحسين أداء قاعدة البيانات</p>
            </div>
            <Database className="h-6 w-6 text-purple-500" />
          </div>
        </button>
      </div>
    </div>
  )
}
