'use client'

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import SuperadminCreateAdminModal from '@/components/superadmin/SuperadminCreateAdminModal'
import SuperadminEditAdminModal from '@/components/superadmin/SuperadminEditAdminModal'
import {
  Shield,
  UserPlus,
  Edit,
  Trash2,
  Eye,
  Lock,
  Unlock,
  Crown,
  Settings,
  Mail,
  Phone,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Filter,
  X,
  User,
  Building
} from 'lucide-react'

interface AdminUser {
  id: string
  email: string
  role: 'admin' | 'super_admin'
  status: 'approved' | 'suspended' | 'blocked' | 'pending' | 'documents_submitted' | 'under_review'
  profile: {
    fullName: string
    phone?: string
    department?: string
  }
  permissions: string[]
  createdAt: string
  lastLogin?: string
  createdBy?: string
}

interface AdminStats {
  totalAdmins: number
  activeAdmins: number
  suspendedAdmins: number
  superAdmins: number
}

export default function SuperAdminManagementPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' })
  const [admins, setAdmins] = useState<AdminUser[]>([])
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingAdmin, setEditingAdmin] = useState<AdminUser | null>(null)

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null
  }
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [hasInitialized, setHasInitialized] = useState(false)

  useEffect(() => {
    // Only fetch once when user is authorized and we haven't initialized yet
    if (isAuthorized && !hasInitialized) {
      setHasInitialized(true)
      fetchAdmins()
    }
  }, [isAuthorized, hasInitialized]) // Only depend on authorization and initialization state

  // Separate useEffect for filter changes
  useEffect(() => {
    if (hasInitialized) {
      fetchAdmins()
    }
  }, [filterRole, filterStatus, searchTerm]) // Fetch when filters change

  const fetchAdmins = async () => {
    try {
      setLoading(true)

      // Fetch real admin data from backend API
      const token = localStorage.getItem('token')
      const params = new URLSearchParams({
        page: '1',
        limit: '50',
        role: filterRole !== 'all' ? filterRole : '',
        status: filterStatus !== 'all' ? filterStatus : '',
        search: searchTerm
      })

      console.log('Making API call to:', `http://localhost:5000/api/superadmin/admins?${params}`)
      console.log('Token:', token ? 'Present' : 'Missing')

      const response = await fetch(`http://localhost:5000/api/superadmin/admins?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      console.log('Response status:', response.status, response.statusText)
      console.log('Response.ok:', response.ok)

      if (response.ok) {
        console.log('✅ API call successful, processing response...')
        const result = await response.json()
        console.log('API Response:', result)
        console.log('Admins data:', result.data?.admins)
        console.log('Stats data:', result.data?.stats)

        const adminsData = result.data?.admins || []
        const statsData = result.data?.stats || {
          totalAdmins: 0,
          activeAdmins: 0,
          suspendedAdmins: 0,
          superAdmins: 0
        }

        console.log('Setting admins data:', adminsData.length, 'admins')
        console.log('First admin sample:', adminsData[0])
        console.log('First admin permissions:', adminsData[0]?.permissions)
        console.log('Setting stats data:', statsData)

        setAdmins(adminsData)
        setStats(statsData)
      } else {
        // API call failed - show empty data instead of fake data
        console.error('❌ API call failed', response.status, response.statusText)
        const errorText = await response.text()
        console.error('Error response:', errorText)

        setAdmins([])
        setStats({
          totalAdmins: 0,
          activeAdmins: 0,
          suspendedAdmins: 0,
          superAdmins: 0
        })
      }
    } catch (error) {
      console.error('Error fetching admins:', error)
    } finally {
      setLoading(false)
    }
  }

  // Stats are now fetched together with admins

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'suspended':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'blocked':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
      case 'documents_submitted':
      case 'under_review':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const getRoleIcon = (role: string) => {
    return role === 'super_admin' ? 
      <Crown className="h-4 w-4 text-purple-500" /> : 
      <Shield className="h-4 w-4 text-blue-500" />
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredAdmins = admins.filter(admin => {
    const matchesSearch = searchTerm === '' ||
      admin.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (admin.profile?.fullName || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = filterRole === 'all' || admin.role === filterRole
    const matchesStatus = filterStatus === 'all' || admin.status === filterStatus

    return matchesSearch && matchesRole && matchesStatus
  })

  // Show loading screen while fetching data
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المدراء...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Shield className="h-8 w-8 text-blue-600 ml-3" />
              إدارة المدراء
            </h1>
            <p className="text-gray-600 mt-2">إدارة حسابات المدراء والصلاحيات</p>
          </div>

          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <UserPlus className="h-4 w-4 ml-2" />
            إضافة مدير جديد
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المدراء</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalAdmins}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المدراء النشطون</p>
                <p className="text-2xl font-bold text-green-600">{stats.activeAdmins}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المدراء المعلقون</p>
                <p className="text-2xl font-bold text-red-600">{stats.suspendedAdmins}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المدراء العامون</p>
                <p className="text-2xl font-bold text-purple-600">{stats.superAdmins}</p>
              </div>
              <Crown className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="البحث بالاسم أو البريد الإلكتروني..."
                className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الدور</label>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع الأدوار</option>
              <option value="super_admin">مدير عام</option>
              <option value="admin">مدير</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع الحالات</option>
              <option value="approved">نشط</option>
              <option value="suspended">معلق</option>
              <option value="blocked">محظور</option>
              <option value="pending">في الانتظار</option>
            </select>
          </div>
        </div>
      </div>

      {/* Admins Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            قائمة المدراء ({filteredAdmins.length})
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المدير
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدور
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  القسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  آخر دخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الصلاحيات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  إجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAdmins.map((admin) => (
                <tr key={admin.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-700">
                            {admin.profile?.fullName?.charAt(0) || admin.email?.charAt(0) || '?'}
                          </span>
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">{admin.profile?.fullName || admin.email}</div>
                        <div className="text-sm text-gray-500">{admin.email}</div>
                        {admin.profile?.phone && (
                          <div className="text-xs text-gray-400 flex items-center mt-1">
                            <Phone className="h-3 w-3 ml-1" />
                            {admin.profile.phone}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getRoleIcon(admin.role)}
                      <span className={`ml-2 text-sm font-medium ${
                        admin.role === 'super_admin' ? 'text-purple-600' : 'text-blue-600'
                      }`}>
                        {admin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(admin.status)}
                      <span className={`ml-2 text-sm ${
                        admin.status === 'approved' ? 'text-green-600' :
                        admin.status === 'suspended' ? 'text-red-600' :
                        admin.status === 'blocked' ? 'text-red-700' :
                        'text-yellow-600'
                      }`}>
                        {admin.status === 'approved' ? 'نشط' :
                         admin.status === 'suspended' ? 'معلق' :
                         admin.status === 'blocked' ? 'محظور' : 'في الانتظار'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {admin.profile?.department || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatTimestamp(admin.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {admin.lastLogin ? formatTimestamp(admin.lastLogin) : 'لم يسجل دخول'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {admin.permissions.slice(0, 2).map((permission) => (
                        <span
                          key={permission}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {permission.replace('_', ' ')}
                        </span>
                      ))}
                      {admin.permissions.length > 2 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          +{admin.permissions.length - 2}
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => setSelectedAdmin(admin)}
                        className="text-blue-600 hover:text-blue-800"
                        title="عرض التفاصيل"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingAdmin(admin)
                          setShowEditModal(true)
                        }}
                        className="text-green-600 hover:text-green-800"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      {admin.status === 'approved' ? (
                        <button
                          className="text-yellow-600 hover:text-yellow-800"
                          title="تعليق"
                        >
                          <Lock className="h-4 w-4" />
                        </button>
                      ) : (
                        <button
                          className="text-green-600 hover:text-green-800"
                          title="إلغاء التعليق"
                        >
                          <Unlock className="h-4 w-4" />
                        </button>
                      )}
                      {admin.role !== 'super_admin' && (
                        <button
                          className="text-red-600 hover:text-red-800"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredAdmins.length === 0 && (
            <div className="text-center py-12">
              <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">لا توجد مدراء تطابق المعايير المحددة</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Admin Modal */}
      {showCreateModal && (
        <SuperadminCreateAdminModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
            fetchAdmins() // Refresh the admin list
          }}
        />
      )}

      {/* Edit Admin Modal */}
      {showEditModal && editingAdmin && (
        <SuperadminEditAdminModal
          isOpen={showEditModal}
          admin={editingAdmin}
          onClose={() => {
            setShowEditModal(false)
            setEditingAdmin(null)
          }}
          onSuccess={() => {
            setShowEditModal(false)
            setEditingAdmin(null)
            fetchAdmins() // Refresh the admin list
          }}
        />
      )}

      {/* Admin Details Modal */}
      {selectedAdmin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <Eye className="h-6 w-6 text-blue-600 ml-3" />
                <h2 className="text-xl font-bold text-gray-900">تفاصيل المدير</h2>
              </div>
              <button
                onClick={() => setSelectedAdmin(null)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <User className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{selectedAdmin.profile?.fullName || 'غير محدد'}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Mail className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{selectedAdmin.email}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Phone className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{selectedAdmin.profile?.phone || 'غير محدد'}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">القسم</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Building className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{selectedAdmin.profile?.department || 'غير محدد'}</span>
                  </div>
                </div>
              </div>

              {/* Role and Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    {getRoleIcon(selectedAdmin.role)}
                    <span className={`ml-2 font-medium ${
                      selectedAdmin.role === 'super_admin' ? 'text-purple-600' : 'text-blue-600'
                    }`}>
                      {selectedAdmin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    {getStatusIcon(selectedAdmin.status)}
                    <span className={`ml-2 font-medium ${
                      selectedAdmin.status === 'approved' ? 'text-green-600' :
                      selectedAdmin.status === 'suspended' ? 'text-red-600' :
                      selectedAdmin.status === 'blocked' ? 'text-red-700' :
                      'text-yellow-600'
                    }`}>
                      {selectedAdmin.status === 'approved' ? 'نشط' :
                       selectedAdmin.status === 'suspended' ? 'معلق' :
                       selectedAdmin.status === 'blocked' ? 'محظور' : 'في الانتظار'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنشاء</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Calendar className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{formatTimestamp(selectedAdmin.createdAt)}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">آخر دخول</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Calendar className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">
                      {selectedAdmin.lastLogin ? formatTimestamp(selectedAdmin.lastLogin) : 'لم يسجل دخول'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Permissions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">الصلاحيات</label>
                <div className="flex flex-wrap gap-2">
                  {selectedAdmin.permissions.map((permission) => (
                    <span
                      key={permission}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {permission.replace('_', ' ')}
                    </span>
                  ))}
                  {selectedAdmin.permissions.length === 0 && (
                    <span className="text-gray-500 text-sm">لا توجد صلاحيات محددة</span>
                  )}
                </div>
              </div>

              {/* Created By */}
              {selectedAdmin.createdBy && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">تم الإنشاء بواسطة</label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <User className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-900">{selectedAdmin.createdBy}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex justify-end p-6 border-t border-gray-200">
              <button
                onClick={() => setSelectedAdmin(null)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
