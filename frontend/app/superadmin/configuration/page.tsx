'use client'

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import {
  Settings,
  Database,
  Mail,
  Globe,
  CreditCard,
  MessageSquare,
  BarChart3,
  RefreshCw,
  Save,
  CheckCircle,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react'

interface PlatformConfig {
  database: {
    host: string
    port: number
    name: string
    username: string
    password: string
    ssl: boolean
    connectionPool: {
      min: number
      max: number
      idle: number
    }
  }
  email: {
    provider: string
    smtp: {
      host: string
      port: number
      secure: boolean
      username: string
      password: string
    }
    sendgrid: {
      apiKey: string
    }
    templates: {
      welcome: string
      passwordReset: string
      auctionWon: string
      bidOutbid: string
    }
  }
  payment: {
    stripe: {
      enabled: boolean
      publishableKey: string
      secretKey: string
      webhookSecret: string
    }
    paypal: {
      enabled: boolean
      clientId: string
      clientSecret: string
      environment: string
    }
    mada: {
      enabled: boolean
      merchantId: string
      terminalId: string
      secretKey: string
    }
  }
  system: {
    environment: string
    logLevel: string
    maintenanceMode: boolean
    allowRegistration: boolean
    requireEmailVerification: boolean
    defaultLanguage: string
    timezone: string
    maxFileSize: number
    allowedFileTypes: string[]
    rateLimit: {
      windowMs: number
      maxRequests: number
    }
  }
  sms: {
    provider: string
    twilio: {
      accountSid: string
      authToken: string
      fromNumber: string
    }
    unifonic: {
      appSid: string
      senderId: string
    }
  }
  analytics: {
    googleAnalytics: {
      enabled: boolean
      trackingId: string
    }
    mixpanel: {
      enabled: boolean
      projectToken: string
    }
  }
}

export default function SuperAdminConfigurationPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' })
  const [config, setConfig] = useState<PlatformConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({})

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null
  }

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('http://localhost:5000/api/configuration', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch configuration')
      }

      const result = await response.json()
      setConfig(result.data)
    } catch (err) {
      console.error('Configuration error:', err)
      setError('فشل في تحميل إعدادات النظام')
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async () => {
    if (!config) return

    try {
      setSaving(true)
      setError(null)
      setSuccessMessage(null)

      const response = await fetch('http://localhost:5000/api/configuration', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      if (!response.ok) {
        throw new Error('Failed to save configuration')
      }

      setSuccessMessage('تم حفظ إعدادات النظام بنجاح')
      setTimeout(() => setSuccessMessage(null), 3000)
    } catch (err) {
      console.error('Save config error:', err)
      setError('فشل في حفظ إعدادات النظام')
    } finally {
      setSaving(false)
    }
  }

  const updateConfig = (section: keyof PlatformConfig, field: string, value: any) => {
    if (!config) return

    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    })
  }

  const updateNestedConfig = (section: keyof PlatformConfig, subsection: string, field: string, value: any) => {
    if (!config) return

    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [subsection]: {
          ...(config[section] as any)[subsection],
          [field]: value
        }
      }
    })
  }

  const togglePasswordVisibility = (key: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل إعدادات النظام...</p>
        </div>
      </div>
    )
  }

  if (error && !config) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchConfig}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center mx-auto"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            إعادة المحاولة
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Settings className="h-8 w-8 text-blue-600 ml-3" />
            إعدادات النظام
          </h1>
          <p className="text-gray-600 mt-2">إدارة إعدادات النظام العامة</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchConfig}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            تحديث
          </button>
          <button
            onClick={saveConfig}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center disabled:opacity-50"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center">
          <CheckCircle className="h-5 w-5 text-green-600 ml-3" />
          <span className="text-green-800">{successMessage}</span>
        </div>
      )}

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 ml-3" />
          <span className="text-red-800">{error}</span>
        </div>
      )}

      {config && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Database Configuration */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Database className="h-5 w-5 text-blue-600 ml-2" />
              إعدادات قاعدة البيانات
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المضيف
                </label>
                <input
                  type="text"
                  value={config.database.host}
                  onChange={(e) => updateConfig('database', 'host', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المنفذ
                </label>
                <input
                  type="number"
                  value={config.database.port}
                  onChange={(e) => updateConfig('database', 'port', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم قاعدة البيانات
                </label>
                <input
                  type="text"
                  value={config.database.name}
                  onChange={(e) => updateConfig('database', 'name', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.database.ssl}
                    onChange={(e) => updateConfig('database', 'ssl', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">استخدام SSL</span>
                </label>
              </div>
            </div>
          </div>

          {/* Email Configuration */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Mail className="h-5 w-5 text-green-600 ml-2" />
              إعدادات البريد الإلكتروني
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مزود الخدمة
                </label>
                <select
                  value={config.email.provider}
                  onChange={(e) => updateConfig('email', 'provider', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="smtp">SMTP</option>
                  <option value="sendgrid">SendGrid</option>
                  <option value="mailgun">Mailgun</option>
                  <option value="ses">Amazon SES</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مضيف SMTP
                </label>
                <input
                  type="text"
                  value={config.email.smtp.host}
                  onChange={(e) => updateNestedConfig('email', 'smtp', 'host', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  منفذ SMTP
                </label>
                <input
                  type="number"
                  value={config.email.smtp.port}
                  onChange={(e) => updateNestedConfig('email', 'smtp', 'port', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم المستخدم
                </label>
                <input
                  type="text"
                  value={config.email.smtp.username}
                  onChange={(e) => updateNestedConfig('email', 'smtp', 'username', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>
          </div>

          {/* System Configuration */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Globe className="h-5 w-5 text-purple-600 ml-2" />
              إعدادات النظام
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  البيئة
                </label>
                <select
                  value={config.system.environment}
                  onChange={(e) => updateConfig('system', 'environment', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="development">تطوير</option>
                  <option value="staging">اختبار</option>
                  <option value="production">إنتاج</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مستوى السجلات
                </label>
                <select
                  value={config.system.logLevel}
                  onChange={(e) => updateConfig('system', 'logLevel', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="error">خطأ</option>
                  <option value="warn">تحذير</option>
                  <option value="info">معلومات</option>
                  <option value="debug">تصحيح</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اللغة الافتراضية
                </label>
                <select
                  value={config.system.defaultLanguage}
                  onChange={(e) => updateConfig('system', 'defaultLanguage', e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="ar">العربية</option>
                  <option value="en">English</option>
                </select>
              </div>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.system.maintenanceMode}
                    onChange={(e) => updateConfig('system', 'maintenanceMode', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">وضع الصيانة</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.system.allowRegistration}
                    onChange={(e) => updateConfig('system', 'allowRegistration', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">السماح بالتسجيل</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.system.requireEmailVerification}
                    onChange={(e) => updateConfig('system', 'requireEmailVerification', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">تتطلب تأكيد البريد الإلكتروني</span>
                </label>
              </div>
            </div>
          </div>

          {/* Payment Configuration */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <CreditCard className="h-5 w-5 text-yellow-600 ml-2" />
              إعدادات الدفع
            </h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Stripe</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.payment.stripe.enabled}
                      onChange={(e) => updateNestedConfig('payment', 'stripe', 'enabled', e.target.checked)}
                      className="ml-2"
                    />
                    <span className="text-sm text-gray-700">تفعيل Stripe</span>
                  </label>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      المفتاح العام
                    </label>
                    <input
                      type="text"
                      value={config.payment.stripe.publishableKey}
                      onChange={(e) => updateNestedConfig('payment', 'stripe', 'publishableKey', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    />
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-800 mb-2">PayPal</h4>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.payment.paypal.enabled}
                      onChange={(e) => updateNestedConfig('payment', 'paypal', 'enabled', e.target.checked)}
                      className="ml-2"
                    />
                    <span className="text-sm text-gray-700">تفعيل PayPal</span>
                  </label>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      البيئة
                    </label>
                    <select
                      value={config.payment.paypal.environment}
                      onChange={(e) => updateNestedConfig('payment', 'paypal', 'environment', e.target.value)}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2"
                    >
                      <option value="sandbox">اختبار</option>
                      <option value="live">مباشر</option>
                    </select>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-800 mb-2">مدى</h4>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.payment.mada.enabled}
                    onChange={(e) => updateNestedConfig('payment', 'mada', 'enabled', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">تفعيل مدى</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
