'use client'

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { superAdminService } from '@/services/superadmin'
import {
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    users: {
      total: number
      recent: number
      byRole: Array<{ _id: string; count: number }>
      byStatus: Array<{ _id: string; count: number }>
      growth: Array<{ _id: string; count: number }>
    }
    auctions: {
      total: number
      active: number
      completed: number
      recent: number
    }
    tenders: {
      total: number
      open: number
      closed: number
      recent: number
    }
    revenue: {
      totalRevenue: number
      monthlyRevenue: number
      growth: number
    }
    activity: {
      total: number
      byCategory: Array<{ _id: string; count: number }>
      bySeverity: Array<{ _id: string; count: number }>
    }
  }
  timeRange: number
  lastUpdated: string
}

export default function SuperAdminAnalyticsPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' })
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('30')

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null
  }

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`http://localhost:5000/api/superadmin/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }

      const result = await response.json()
      setAnalytics(result.data)
    } catch (err) {
      console.error('Analytics error:', err)
      setError('فشل في تحميل التحليلات')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التحليلات...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchAnalytics}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center mx-auto"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            إعادة المحاولة
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="h-8 w-8 text-blue-600 ml-3" />
            التحليلات المتقدمة
          </h1>
          <p className="text-gray-600 mt-2">تحليلات شاملة وتقارير مفصلة للمنصة</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 bg-white"
          >
            <option value="7">آخر 7 أيام</option>
            <option value="30">آخر 30 يوم</option>
            <option value="90">آخر 90 يوم</option>
            <option value="365">آخر سنة</option>
          </select>
          <button
            onClick={fetchAnalytics}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            تحديث
          </button>
        </div>
      </div>

      {analytics && (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Users Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.overview.users.total.toLocaleString()}</p>
                  <p className="text-sm text-green-600">+{analytics.overview.users.recent} جديد</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            {/* Auctions Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المزادات</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.overview.auctions.total}</p>
                  <p className="text-sm text-blue-600">{analytics.overview.auctions.active} نشط</p>
                </div>
                <Activity className="h-8 w-8 text-green-600" />
              </div>
            </div>

            {/* Tenders Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المناقصات</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.overview.tenders.total}</p>
                  <p className="text-sm text-purple-600">{analytics.overview.tenders.open} مفتوح</p>
                </div>
                <CheckCircle className="h-8 w-8 text-purple-600" />
              </div>
            </div>

            {/* Revenue Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الإيرادات</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.overview.revenue.totalRevenue.toLocaleString()} ر.س</p>
                  <p className="text-sm text-green-600">+{analytics.overview.revenue.growth}%</p>
                </div>
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
            </div>
          </div>

          {/* Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Distribution */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع المستخدمين حسب النوع</h3>
              <div className="space-y-3">
                {analytics.overview.users.byRole.map((role) => (
                  <div key={role._id} className="flex justify-between items-center">
                    <span className="text-gray-600">{role._id === 'company' ? 'شركات' : role._id === 'individual' ? 'أفراد' : role._id === 'government' ? 'حكومي' : role._id}</span>
                    <span className="font-semibold">{role.count}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Activity Distribution */}
            <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع النشاط</h3>
              <div className="space-y-3">
                {analytics.overview.activity.byCategory.map((category) => (
                  <div key={category._id} className="flex justify-between items-center">
                    <span className="text-gray-600">{category._id}</span>
                    <span className="font-semibold">{category.count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Last Updated */}
          <div className="mt-6 text-center text-sm text-gray-500">
            آخر تحديث: {new Date(analytics.lastUpdated).toLocaleString('ar-SA')}
          </div>
        </>
      )}
    </div>
  )
}
