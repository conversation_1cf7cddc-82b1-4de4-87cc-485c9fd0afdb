'use client'

import { useState, useEffect } from 'react'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import {
  Shield,
  Lock,
  Key,
  AlertTriangle,
  Settings,
  Users,
  Clock,
  Globe,
  RefreshCw,
  Save,
  CheckCircle
} from 'lucide-react'

interface SecuritySettings {
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    maxAge: number
    preventReuse: number
  }
  sessionSettings: {
    maxDuration: number
    idleTimeout: number
    maxConcurrentSessions: number
    requireReauth: boolean
  }
  accessControl: {
    enableIPRestrictions: boolean
    allowedIPs: string[]
    blockedIPs: string[]
    enableGeoBlocking: boolean
    allowedCountries: string[]
    blockedCountries: string[]
  }
  securityMonitoring: {
    enableFailedLoginTracking: boolean
    maxFailedAttempts: number
    lockoutDuration: number
    enableSuspiciousActivityDetection: boolean
    enableRealTimeAlerts: boolean
  }
  twoFactorAuth: {
    enforceForAdmins: boolean
    enforceForUsers: boolean
    allowedMethods: string[]
  }
}

export default function SuperAdminSecurityPage() {
  const { user, isAuthorized } = useAdminAuth({ requiredRole: 'super_admin' })
  const [settings, setSettings] = useState<SecuritySettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Don't render if user is not authorized
  if (!isAuthorized) {
    return null
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('http://localhost:5000/api/security/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch security settings')
      }

      const result = await response.json()
      setSettings(result.data)
    } catch (err) {
      console.error('Security settings error:', err)
      setError('فشل في تحميل إعدادات الأمان')
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    if (!settings) return

    try {
      setSaving(true)
      setError(null)
      setSuccessMessage(null)

      const response = await fetch('http://localhost:5000/api/security/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      })

      if (!response.ok) {
        throw new Error('Failed to save security settings')
      }

      setSuccessMessage('تم حفظ إعدادات الأمان بنجاح')
      setTimeout(() => setSuccessMessage(null), 3000)
    } catch (err) {
      console.error('Save settings error:', err)
      setError('فشل في حفظ إعدادات الأمان')
    } finally {
      setSaving(false)
    }
  }

  const updateSettings = (section: keyof SecuritySettings, field: string, value: any) => {
    if (!settings) return

    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [field]: value
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل إعدادات الأمان...</p>
        </div>
      </div>
    )
  }

  if (error && !settings) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchSettings}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center mx-auto"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            إعادة المحاولة
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Shield className="h-8 w-8 text-blue-600 ml-3" />
            الأمان والحماية
          </h1>
          <p className="text-gray-600 mt-2">إدارة إعدادات الأمان والحماية</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchSettings}
            className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center"
          >
            <RefreshCw className="h-4 w-4 ml-2" />
            تحديث
          </button>
          <button
            onClick={saveSettings}
            disabled={saving}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center disabled:opacity-50"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
            ) : (
              <Save className="h-4 w-4 ml-2" />
            )}
            {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {successMessage && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center">
          <CheckCircle className="h-5 w-5 text-green-600 ml-3" />
          <span className="text-green-800">{successMessage}</span>
        </div>
      )}

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 ml-3" />
          <span className="text-red-800">{error}</span>
        </div>
      )}

      {settings && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Password Policy */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Lock className="h-5 w-5 text-blue-600 ml-2" />
              سياسة كلمات المرور
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الحد الأدنى لطول كلمة المرور
                </label>
                <input
                  type="number"
                  min="6"
                  max="32"
                  value={settings.passwordPolicy.minLength}
                  onChange={(e) => updateSettings('passwordPolicy', 'minLength', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مدة انتهاء كلمة المرور (أيام)
                </label>
                <input
                  type="number"
                  min="30"
                  max="365"
                  value={settings.passwordPolicy.maxAge}
                  onChange={(e) => updateSettings('passwordPolicy', 'maxAge', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.passwordPolicy.requireUppercase}
                    onChange={(e) => updateSettings('passwordPolicy', 'requireUppercase', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">يتطلب أحرف كبيرة</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.passwordPolicy.requireNumbers}
                    onChange={(e) => updateSettings('passwordPolicy', 'requireNumbers', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">يتطلب أرقام</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.passwordPolicy.requireSpecialChars}
                    onChange={(e) => updateSettings('passwordPolicy', 'requireSpecialChars', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">يتطلب رموز خاصة</span>
                </label>
              </div>
            </div>
          </div>

          {/* Session Settings */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Clock className="h-5 w-5 text-green-600 ml-2" />
              إعدادات الجلسة
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مدة الجلسة القصوى (دقائق)
                </label>
                <input
                  type="number"
                  min="60"
                  max="1440"
                  value={settings.sessionSettings.maxDuration}
                  onChange={(e) => updateSettings('sessionSettings', 'maxDuration', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مهلة عدم النشاط (دقائق)
                </label>
                <input
                  type="number"
                  min="5"
                  max="120"
                  value={settings.sessionSettings.idleTimeout}
                  onChange={(e) => updateSettings('sessionSettings', 'idleTimeout', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الحد الأقصى للجلسات المتزامنة
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={settings.sessionSettings.maxConcurrentSessions}
                  onChange={(e) => updateSettings('sessionSettings', 'maxConcurrentSessions', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>
          </div>

          {/* Security Monitoring */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 ml-2" />
              مراقبة الأمان
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الحد الأقصى لمحاولات تسجيل الدخول الفاشلة
                </label>
                <input
                  type="number"
                  min="3"
                  max="10"
                  value={settings.securityMonitoring.maxFailedAttempts}
                  onChange={(e) => updateSettings('securityMonitoring', 'maxFailedAttempts', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  مدة القفل (دقائق)
                </label>
                <input
                  type="number"
                  min="5"
                  max="60"
                  value={settings.securityMonitoring.lockoutDuration}
                  onChange={(e) => updateSettings('securityMonitoring', 'lockoutDuration', parseInt(e.target.value))}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.securityMonitoring.enableFailedLoginTracking}
                    onChange={(e) => updateSettings('securityMonitoring', 'enableFailedLoginTracking', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">تتبع محاولات تسجيل الدخول الفاشلة</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.securityMonitoring.enableSuspiciousActivityDetection}
                    onChange={(e) => updateSettings('securityMonitoring', 'enableSuspiciousActivityDetection', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">كشف النشاط المشبوه</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.securityMonitoring.enableRealTimeAlerts}
                    onChange={(e) => updateSettings('securityMonitoring', 'enableRealTimeAlerts', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">التنبيهات الفورية</span>
                </label>
              </div>
            </div>
          </div>

          {/* Two Factor Authentication */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Key className="h-5 w-5 text-purple-600 ml-2" />
              المصادقة الثنائية
            </h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.twoFactorAuth.enforceForAdmins}
                    onChange={(e) => updateSettings('twoFactorAuth', 'enforceForAdmins', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">إجبارية للمدراء</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.twoFactorAuth.enforceForUsers}
                    onChange={(e) => updateSettings('twoFactorAuth', 'enforceForUsers', e.target.checked)}
                    className="ml-2"
                  />
                  <span className="text-sm text-gray-700">إجبارية للمستخدمين</span>
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الطرق المسموحة
                </label>
                <div className="space-y-1">
                  {['totp', 'sms', 'email'].map((method) => (
                    <label key={method} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.twoFactorAuth.allowedMethods.includes(method)}
                        onChange={(e) => {
                          const methods = e.target.checked
                            ? [...settings.twoFactorAuth.allowedMethods, method]
                            : settings.twoFactorAuth.allowedMethods.filter(m => m !== method)
                          updateSettings('twoFactorAuth', 'allowedMethods', methods)
                        }}
                        className="ml-2"
                      />
                      <span className="text-sm text-gray-700">
                        {method === 'totp' ? 'تطبيق المصادقة' : method === 'sms' ? 'رسائل نصية' : 'بريد إلكتروني'}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
