'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  FileText,
  Search,
  Calendar,
  Building,
  DollarSign,
  Users,
  Clock,
  Eye,
  Download,
  ArrowRight,
  Shield,
  CheckCircle,
  Target,
  Award
} from 'lucide-react';
import Link from 'next/link';
import api from '@/lib/api';

export default function TendersPublicPage() {
  const [featuredTenders, setFeaturedTenders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTenders();
  }, []);

  const loadTenders = async () => {
    try {
      setLoading(true);
      const response = await api.get('/tenders?limit=6&status=open');

      if (response.data.success) {
        setFeaturedTenders(response.data.data.tenders || []);
      } else if (response.data && Array.isArray(response.data)) {
        setFeaturedTenders(response.data);
      }
    } catch (error) {
      console.error('Error loading tenders:', error);
      // Show empty state when API fails - no mock data
      setFeaturedTenders([]);
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    { name: "إنشاءات", count: 25, icon: "🏗️" },
    { name: "تقنية", count: 18, icon: "💻" },
    { name: "خدمات", count: 32, icon: "🔧" },
    { name: "توريدات", count: 28, icon: "📦" },
    { name: "استشارات", count: 12, icon: "💼" },
    { name: "صحة", count: 15, icon: "🏥" }
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date();
    const end = new Date(deadline);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return 'انتهت';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days} يوم متبقي`;
    return `${hours} ساعة متبقية`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-green-100 text-green-800">المناقصات الحكومية</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            فرص <span className="text-green-600">المناقصات الحكومية</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            اكتشف أحدث المناقصات الحكومية وقدم عطاءاتك للمشاريع الكبرى في المملكة
          </p>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">85+</div>
              <div className="text-sm text-gray-600">مناقصة نشطة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">450+</div>
              <div className="text-sm text-gray-600">شركة مسجلة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">2.5B+</div>
              <div className="text-sm text-gray-600">ريال قيمة المشاريع</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">98%</div>
              <div className="text-sm text-gray-600">معدل الشفافية</div>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">تصفح حسب القطاع</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h3 className="font-semibold text-gray-900 mb-1">{category.name}</h3>
                  <p className="text-sm text-gray-600">{category.count} مناقصة</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Featured Tenders */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">المناقصات المميزة</h2>
            <Link href="/auth/register">
              <Button variant="outline">
                عرض الكل
                <ArrowRight className="h-4 w-4 mr-2" />
              </Button>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="bg-gray-200 animate-pulse h-20"></CardHeader>
                  <CardContent className="p-6">
                    <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                  </CardContent>
                </Card>
              ))
            ) : featuredTenders.length > 0 ? (
              featuredTenders.map((tender) => (
              <Card key={tender.id} className="overflow-hidden hover:shadow-xl transition-all duration-300">
                <CardHeader className="bg-gradient-to-r from-green-500 to-blue-500 text-white">
                  <div className="flex items-center justify-between">
                    <Badge className="bg-white text-green-600">مميز</Badge>
                    <Badge variant="outline" className="border-white text-white">
                      {tender.category}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                    {tender.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {tender.description}
                  </p>
                  
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">الميزانية المقدرة:</span>
                      <span className="text-lg font-bold text-green-600">
                        {formatPrice(tender.budget)}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{tender.entity}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatTimeRemaining(tender.deadline)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {tender.applicants} متقدم
                    </div>
                  </div>
                  
                  <Link href="/auth/register">
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      <FileText className="h-4 w-4 ml-2" />
                      قدم عطاءك
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))
            ) : (
              // Empty state
              <div className="col-span-full text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مناقصات متاحة حالياً</h3>
                <p className="text-gray-600">تحقق مرة أخرى لاحقاً للمناقصات الجديدة</p>
              </div>
            )}
          </div>
        </div>

        {/* How It Works */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">كيف تعمل المناقصات؟</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">1</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">سجل شركتك</h3>
              <p className="text-gray-600 text-sm">أنشئ حساب شركة وأكمل التوثيق المطلوب</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">2</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">تصفح المناقصات</h3>
              <p className="text-gray-600 text-sm">ابحث عن المناقصات المناسبة لتخصصك</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">قدم عطاءك</h3>
              <p className="text-gray-600 text-sm">أعد عطاءك وقدمه قبل انتهاء الموعد</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">اربح المشروع</h3>
              <p className="text-gray-600 text-sm">احصل على المشروع وابدأ التنفيذ</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold mb-4">جاهز للمشاركة في المناقصات؟</h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى مئات الشركات واحصل على أفضل المشاريع الحكومية
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                سجل شركتك الآن
              </Button>
            </Link>
            <Link href="/government/tenders">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-green-600">
                تصفح المناقصات
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
