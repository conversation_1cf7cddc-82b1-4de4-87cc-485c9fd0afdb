'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import api from '@/lib/api'
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Palette,
  Globe,
  Mail,
  Phone,
  Lock,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  Trash2,
  Al<PERSON><PERSON>riangle
} from 'lucide-react'

interface UserSettings {
  notifications: {
    email: boolean
    sms: boolean
    push: boolean
    newBids: boolean
    auctionEnding: boolean
    applicationUpdates: boolean
    paymentReminders: boolean
    marketingEmails: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'private' | 'contacts'
    showEmail: boolean
    showPhone: boolean
    showBidHistory: boolean
  }
  preferences: {
    language: 'ar' | 'en'
    currency: 'SAR' | 'USD' | 'EUR'
    timezone: string
    theme: 'light' | 'dark' | 'auto'
    itemsPerPage: number
  }
  security: {
    twoFactorEnabled: boolean
    loginNotifications: boolean
    sessionTimeout: number
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPasswords, setShowPasswords] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const response = await api.get('/users/settings')
      
      if (response.data.success) {
        setSettings(response.data.data)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
      // Show empty/default settings when API fails - no mock data
      setSettings({
        notifications: {
          email: false,
          sms: false,
          push: false,
          newBids: false,
          auctionEnding: false,
          applicationUpdates: false,
          paymentReminders: false,
          marketingEmails: false
        },
        privacy: {
          profileVisibility: 'private',
          showEmail: false,
          showPhone: false,
          showBidHistory: false
        },
        preferences: {
          language: 'ar',
          currency: 'SAR',
          timezone: 'Asia/Riyadh',
          theme: 'light',
          itemsPerPage: 10
        },
        security: {
          twoFactorEnabled: false,
          loginNotifications: false,
          sessionTimeout: 30
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const saveSettings = async () => {
    try {
      setSaving(true)
      const response = await api.patch('/users/settings', settings)
      
      if (response.data.success) {
        toast({
          title: 'تم الحفظ',
          description: 'تم حفظ الإعدادات بنجاح'
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ في حفظ الإعدادات',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const changePassword = async () => {
    if (newPassword !== confirmPassword) {
      toast({
        title: 'خطأ',
        description: 'كلمات المرور الجديدة غير متطابقة',
        variant: 'destructive'
      })
      return
    }

    if (newPassword.length < 8) {
      toast({
        title: 'خطأ',
        description: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await api.patch('/users/change-password', {
        currentPassword,
        newPassword
      })
      
      if (response.data.success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تغيير كلمة المرور بنجاح'
        })
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')
      }
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: error.response?.data?.message || 'حدث خطأ في تغيير كلمة المرور',
        variant: 'destructive'
      })
    }
  }

  const updateSetting = (section: keyof UserSettings, key: string, value: any) => {
    if (!settings) return
    
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    })
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل الإعدادات...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['user', 'company', 'government', 'admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-600 to-gray-800 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">الإعدادات</h1>
              <p className="text-gray-100 mt-1">إدارة تفضيلاتك وإعدادات الحساب</p>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                onClick={loadSettings}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
              <Button 
                onClick={saveSettings}
                disabled={saving}
                className="bg-white text-gray-800 hover:bg-gray-100"
              >
                {saving ? (
                  <RefreshCw className="h-4 w-4 animate-spin ml-2" />
                ) : (
                  <Save className="h-4 w-4 ml-2" />
                )}
                حفظ التغييرات
              </Button>
            </div>
          </div>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              الإشعارات
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              الخصوصية
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              التفضيلات
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              الأمان
            </TabsTrigger>
          </TabsList>

          {/* Notifications Settings */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  إعدادات الإشعارات
                </CardTitle>
                <CardDescription>
                  اختر كيف ومتى تريد استلام الإشعارات
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">طرق الإشعار</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-notifications">البريد الإلكتروني</Label>
                        <p className="text-sm text-gray-600">استلام الإشعارات عبر البريد الإلكتروني</p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={settings?.notifications.email}
                        onCheckedChange={(checked) => updateSetting('notifications', 'email', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sms-notifications">الرسائل النصية</Label>
                        <p className="text-sm text-gray-600">استلام الإشعارات عبر الرسائل النصية</p>
                      </div>
                      <Switch
                        id="sms-notifications"
                        checked={settings?.notifications.sms}
                        onCheckedChange={(checked) => updateSetting('notifications', 'sms', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="push-notifications">الإشعارات الفورية</Label>
                        <p className="text-sm text-gray-600">استلام الإشعارات الفورية في المتصفح</p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={settings?.notifications.push}
                        onCheckedChange={(checked) => updateSetting('notifications', 'push', checked)}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-4">أنواع الإشعارات</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="new-bids">مزايدات جديدة</Label>
                        <p className="text-sm text-gray-600">عند تقديم مزايدات جديدة على مزاداتك</p>
                      </div>
                      <Switch
                        id="new-bids"
                        checked={settings?.notifications.newBids}
                        onCheckedChange={(checked) => updateSetting('notifications', 'newBids', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auction-ending">انتهاء المزادات</Label>
                        <p className="text-sm text-gray-600">تذكير قبل انتهاء المزادات</p>
                      </div>
                      <Switch
                        id="auction-ending"
                        checked={settings?.notifications.auctionEnding}
                        onCheckedChange={(checked) => updateSetting('notifications', 'auctionEnding', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="application-updates">تحديثات الطلبات</Label>
                        <p className="text-sm text-gray-600">عند تحديث حالة طلباتك في المناقصات</p>
                      </div>
                      <Switch
                        id="application-updates"
                        checked={settings?.notifications.applicationUpdates}
                        onCheckedChange={(checked) => updateSetting('notifications', 'applicationUpdates', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="payment-reminders">تذكير المدفوعات</Label>
                        <p className="text-sm text-gray-600">تذكير بالمدفوعات المستحقة</p>
                      </div>
                      <Switch
                        id="payment-reminders"
                        checked={settings?.notifications.paymentReminders}
                        onCheckedChange={(checked) => updateSetting('notifications', 'paymentReminders', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="marketing-emails">الرسائل التسويقية</Label>
                        <p className="text-sm text-gray-600">استلام العروض والأخبار التسويقية</p>
                      </div>
                      <Switch
                        id="marketing-emails"
                        checked={settings?.notifications.marketingEmails}
                        onCheckedChange={(checked) => updateSetting('notifications', 'marketingEmails', checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Privacy Settings */}
          <TabsContent value="privacy">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  إعدادات الخصوصية
                </CardTitle>
                <CardDescription>
                  تحكم في من يمكنه رؤية معلوماتك
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="profile-visibility">مستوى ظهور الملف الشخصي</Label>
                  <Select
                    value={settings?.privacy.profileVisibility}
                    onValueChange={(value) => updateSetting('privacy', 'profileVisibility', value)}
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">عام - يمكن للجميع رؤيته</SelectItem>
                      <SelectItem value="contacts">جهات الاتصال فقط</SelectItem>
                      <SelectItem value="private">خاص - مخفي عن الآخرين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="show-email">إظهار البريد الإلكتروني</Label>
                      <p className="text-sm text-gray-600">السماح للآخرين برؤية بريدك الإلكتروني</p>
                    </div>
                    <Switch
                      id="show-email"
                      checked={settings?.privacy.showEmail}
                      onCheckedChange={(checked) => updateSetting('privacy', 'showEmail', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="show-phone">إظهار رقم الهاتف</Label>
                      <p className="text-sm text-gray-600">السماح للآخرين برؤية رقم هاتفك</p>
                    </div>
                    <Switch
                      id="show-phone"
                      checked={settings?.privacy.showPhone}
                      onCheckedChange={(checked) => updateSetting('privacy', 'showPhone', checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="show-bid-history">إظهار تاريخ المزايدات</Label>
                      <p className="text-sm text-gray-600">السماح للآخرين برؤية تاريخ مزايداتك</p>
                    </div>
                    <Switch
                      id="show-bid-history"
                      checked={settings?.privacy.showBidHistory}
                      onCheckedChange={(checked) => updateSetting('privacy', 'showBidHistory', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preferences Settings */}
          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  التفضيلات العامة
                </CardTitle>
                <CardDescription>
                  تخصيص تجربة استخدام المنصة
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="language">اللغة</Label>
                    <Select
                      value={settings?.preferences.language}
                      onValueChange={(value) => updateSetting('preferences', 'language', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="currency">العملة</Label>
                    <Select
                      value={settings?.preferences.currency}
                      onValueChange={(value) => updateSetting('preferences', 'currency', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                        <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                        <SelectItem value="EUR">يورو (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="theme">المظهر</Label>
                    <Select
                      value={settings?.preferences.theme}
                      onValueChange={(value) => updateSetting('preferences', 'theme', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">فاتح</SelectItem>
                        <SelectItem value="dark">داكن</SelectItem>
                        <SelectItem value="auto">تلقائي</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="items-per-page">عدد العناصر في الصفحة</Label>
                    <Select
                      value={settings?.preferences.itemsPerPage.toString()}
                      onValueChange={(value) => updateSetting('preferences', 'itemsPerPage', parseInt(value))}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 عناصر</SelectItem>
                        <SelectItem value="10">10 عناصر</SelectItem>
                        <SelectItem value="20">20 عنصر</SelectItem>
                        <SelectItem value="50">50 عنصر</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Settings */}
          <TabsContent value="security">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    إعدادات الأمان
                  </CardTitle>
                  <CardDescription>
                    حماية حسابك وبياناتك
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="two-factor">المصادقة الثنائية</Label>
                        <p className="text-sm text-gray-600">إضافة طبقة حماية إضافية لحسابك</p>
                      </div>
                      <Switch
                        id="two-factor"
                        checked={settings?.security.twoFactorEnabled}
                        onCheckedChange={(checked) => updateSetting('security', 'twoFactorEnabled', checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="login-notifications">إشعارات تسجيل الدخول</Label>
                        <p className="text-sm text-gray-600">إشعار عند تسجيل الدخول من جهاز جديد</p>
                      </div>
                      <Switch
                        id="login-notifications"
                        checked={settings?.security.loginNotifications}
                        onCheckedChange={(checked) => updateSetting('security', 'loginNotifications', checked)}
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="session-timeout">انتهاء الجلسة (بالدقائق)</Label>
                    <Select
                      value={settings?.security.sessionTimeout.toString()}
                      onValueChange={(value) => updateSetting('security', 'sessionTimeout', parseInt(value))}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 دقيقة</SelectItem>
                        <SelectItem value="30">30 دقيقة</SelectItem>
                        <SelectItem value="60">ساعة واحدة</SelectItem>
                        <SelectItem value="120">ساعتان</SelectItem>
                        <SelectItem value="480">8 ساعات</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Change Password */}
              <Card>
                <CardHeader>
                  <CardTitle>تغيير كلمة المرور</CardTitle>
                  <CardDescription>
                    تحديث كلمة مرور حسابك
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="current-password">كلمة المرور الحالية</Label>
                    <div className="relative mt-2">
                      <Input
                        id="current-password"
                        type={showPasswords ? 'text' : 'password'}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        placeholder="أدخل كلمة المرور الحالية"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute left-2 top-1/2 transform -translate-y-1/2"
                        onClick={() => setShowPasswords(!showPasswords)}
                      >
                        {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
                    <Input
                      id="new-password"
                      type={showPasswords ? 'text' : 'password'}
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="أدخل كلمة المرور الجديدة"
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirm-password">تأكيد كلمة المرور</Label>
                    <Input
                      id="confirm-password"
                      type={showPasswords ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                      className="mt-2"
                    />
                  </div>

                  <Button 
                    onClick={changePassword}
                    disabled={!currentPassword || !newPassword || !confirmPassword}
                  >
                    تغيير كلمة المرور
                  </Button>
                </CardContent>
              </Card>

              {/* Danger Zone */}
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="text-red-600 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    منطقة الخطر
                  </CardTitle>
                  <CardDescription>
                    إجراءات لا يمكن التراجع عنها
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="destructive" className="w-full">
                    <Trash2 className="h-4 w-4 ml-2" />
                    حذف الحساب نهائياً
                  </Button>
                  <p className="text-sm text-gray-600 mt-2">
                    سيتم حذف جميع بياناتك نهائياً ولا يمكن استرجاعها
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
