// Super Admin API service
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// System Logs interfaces
export interface SystemLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  category: string;
  action: string;
  user: {
    id: string;
    email: string;
    role: string;
  } | null;
  details: string;
  ipAddress: string;
  userAgent?: string;
  metadata?: any;
}

export interface SystemLogsResponse {
  logs: SystemLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: {
    totalLogs: number;
    errorCount: number;
    warningCount: number;
    securityEvents: number;
    adminActions: number;
    userActions: number;
  };
}

// Admin Management interfaces
export interface AdminUser {
  id: string;
  email: string;
  role: 'admin' | 'super_admin';
  status: string;
  profile: {
    fullName: string;
    phone?: string;
    department?: string;
  };
  permissions: string[];
  createdAt: string;
  lastLogin?: string;
  createdBy?: string;
}

export interface AdminsResponse {
  admins: AdminUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: {
    totalAdmins: number;
    activeAdmins: number;
    suspendedAdmins: number;
    superAdmins: number;
  };
}

// System Health interfaces
export interface SystemHealth {
  overall: string;
  uptime: number;
  lastUpdated: string;
  services: {
    [key: string]: {
      status: string;
      responseTime: number;
      uptime: number;
      lastCheck: string;
      errorRate: number;
    };
  };
  performance: {
    [key: string]: {
      current: number;
      average: number;
      peak: number;
      trend: string;
    };
  };
  alerts: Array<{
    id: string;
    type: string;
    title: string;
    description: string;
    timestamp: string;
    resolved: boolean;
    service: string;
  }>;
}

// Analytics interfaces
export interface AnalyticsOverview {
  users: {
    total: number;
    recent: number;
    byRole: Array<{ _id: string; count: number }>;
    byStatus: Array<{ _id: string; count: number }>;
    growth: Array<{ _id: string; count: number }>;
  };
  auctions: {
    total: number;
    active: number;
    completed: number;
    revenue: number;
  };
  tenders: {
    total: number;
    open: number;
    closed: number;
    awarded: number;
  };
  system: {
    uptime: number;
    performance: number;
    errors: number;
    warnings: number;
  };
}

export interface AnalyticsResponse {
  overview: AnalyticsOverview;
  timeRange: number;
  lastUpdated: string;
}

class SuperAdminService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  // ==================== ANALYTICS ====================
  async getAnalytics(params: {
    timeRange?: number;
  } = {}): Promise<AnalyticsResponse> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/superadmin/analytics?${queryParams}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch analytics data');
    }

    const result: ApiResponse<AnalyticsResponse> = await response.json();
    return result.data;
  }

  // ==================== SYSTEM LOGS ====================
  async getSystemLogs(params: {
    page?: number;
    limit?: number;
    category?: string;
    level?: string;
    search?: string;
    timeRange?: string;
  } = {}): Promise<SystemLogsResponse> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/superadmin/logs?${queryParams}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch system logs');
    }

    const result: ApiResponse<SystemLogsResponse> = await response.json();
    return result.data;
  }

  // ==================== ADMIN MANAGEMENT ====================
  async getAdmins(params: {
    page?: number;
    limit?: number;
    role?: string;
    status?: string;
    search?: string;
  } = {}): Promise<AdminsResponse> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await fetch(`${API_BASE_URL}/superadmin/admins?${queryParams}`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch admin users');
    }

    const result: ApiResponse<AdminsResponse> = await response.json();
    return result.data;
  }

  async createAdmin(adminData: {
    email: string;
    password: string;
    fullName: string;
    role: 'admin' | 'super_admin';
    department?: string;
    phone?: string;
  }): Promise<AdminUser> {
    const response = await fetch(`${API_BASE_URL}/superadmin/admins`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(adminData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create admin user');
    }

    const result: ApiResponse<{ admin: AdminUser }> = await response.json();
    return result.data.admin;
  }

  async updateAdmin(id: string, updates: Partial<AdminUser>): Promise<AdminUser> {
    const response = await fetch(`${API_BASE_URL}/superadmin/admins/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update admin user');
    }

    const result: ApiResponse<{ admin: AdminUser }> = await response.json();
    return result.data.admin;
  }

  async deleteAdmin(id: string): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/superadmin/admins/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete admin user');
    }
  }

  // ==================== SYSTEM HEALTH ====================
  async getSystemHealth(): Promise<SystemHealth> {
    const response = await fetch(`${API_BASE_URL}/superadmin/system-health`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch system health');
    }

    const result: ApiResponse<SystemHealth> = await response.json();
    return result.data;
  }

  // ==================== SECURITY MANAGEMENT ====================
  async getSecuritySettings(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/security/settings`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch security settings');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  async updateSecuritySettings(settings: any): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/security/settings`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(settings)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update security settings');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  async testSecurityConnection(service: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/security/test-connection`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ service })
    });

    if (!response.ok) {
      throw new Error('Failed to test security connection');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  // ==================== PLATFORM CONFIGURATION ====================
  async getPlatformConfiguration(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/configuration`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to fetch platform configuration');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  async updatePlatformConfiguration(config: any): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/configuration`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update platform configuration');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  async testServiceConnection(service: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/configuration/test-connection`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ service })
    });

    if (!response.ok) {
      throw new Error('Failed to test service connection');
    }

    const result: ApiResponse<any> = await response.json();
    return result.data;
  }

  async exportConfiguration(): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/configuration/export`, {
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to export configuration');
    }

    return response.blob();
  }
}

export const superAdminService = new SuperAdminService();