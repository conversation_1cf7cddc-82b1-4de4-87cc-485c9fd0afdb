require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function debugPassword() {
  try {
    // Find the test admin user
    const user = await User.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.log('❌ User not found');
      mongoose.connection.close();
      return;
    }
    
    console.log('👤 User found:', user.email);
    console.log('🔑 Stored password hash:', user.password);
    console.log('📏 Hash length:', user.password.length);
    
    // Test different password comparisons
    const testPassword = 'admin123';
    console.log('\n🧪 Testing password comparisons:');
    
    // Direct bcrypt compare
    const directCompare = await bcrypt.compare(testPassword, user.password);
    console.log('🔍 Direct bcrypt.compare result:', directCompare);
    
    // User method compare
    const methodCompare = await user.comparePassword(testPassword);
    console.log('🔍 User.comparePassword result:', methodCompare);
    
    // Test with wrong password
    const wrongCompare = await user.comparePassword('wrongpassword');
    console.log('🔍 Wrong password result:', wrongCompare);
    
    // Create a fresh hash and test
    console.log('\n🆕 Creating fresh hash:');
    const salt = await bcrypt.genSalt(12);
    const freshHash = await bcrypt.hash(testPassword, salt);
    console.log('🔑 Fresh hash:', freshHash);
    
    const freshCompare = await bcrypt.compare(testPassword, freshHash);
    console.log('🔍 Fresh hash compare result:', freshCompare);
    
    mongoose.connection.close();
  } catch (error) {
    console.error('❌ Error:', error);
    mongoose.connection.close();
  }
}

debugPassword();
