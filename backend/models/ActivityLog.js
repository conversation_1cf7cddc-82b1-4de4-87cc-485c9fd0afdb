const mongoose = require('mongoose');

const activityLogSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      // Authentication actions
      'login', 'logout', 'register', 'password_reset', 'password_change',
      
      // Profile actions
      'profile_update', 'profile_view', 'avatar_change',
      
      // Auction actions
      'auction_create', 'auction_update', 'auction_delete', 'auction_view',
      'auction_bid', 'auction_withdraw_bid', 'auction_favorite', 'auction_unfavorite',
      'auction_watch', 'auction_unwatch', 'auction_share',
      
      // Tender actions
      'tender_create', 'tender_update', 'tender_delete', 'tender_view',
      'tender_propose', 'tender_withdraw_proposal', 'tender_favorite', 'tender_unfavorite',
      'tender_watch', 'tender_unwatch', 'tender_share',
      
      // Company actions
      'company_register', 'company_update', 'company_verify',
      
      // Government actions
      'government_approve', 'government_reject', 'government_review',
      
      // Admin actions
      'admin_user_ban', 'admin_user_unban', 'admin_user_delete',
      'admin_auction_approve', 'admin_auction_reject',
      'admin_tender_approve', 'admin_tender_reject',
      'admin_created', 'admin_updated', 'admin_deleted',
      
      // Messaging actions
      'message_send', 'message_read', 'conversation_create',
      
      // System actions
      'payment_success', 'payment_failed', 'notification_sent',
      'email_sent', 'file_upload', 'file_delete', 'file_export',
      'system_test', 'configuration_update'
    ]
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  category: {
    type: String,
    enum: ['authentication', 'admin', 'system', 'security', 'database', 'api', 'user'],
    default: 'system'
  },
  targetType: {
    type: String,
    enum: ['auction', 'tender', 'user', 'company', 'bid', 'proposal', 'message', 'payment', 'file', 'system']
  },
  targetId: {
    type: mongoose.Schema.Types.ObjectId
  },
  metadata: {
    // Flexible object for storing additional data
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  location: {
    country: String,
    city: String,
    coordinates: {
      lat: Number,
      lng: Number
    }
  },
  severity: {
    type: String,
    enum: ['info', 'warning', 'error', 'critical'],
    default: 'info'
  },
  isSuccessful: {
    type: Boolean,
    default: true
  },
  errorMessage: {
    type: String
  },
  duration: {
    type: Number // in milliseconds
  }
}, {
  timestamps: true
});

// Indexes for better query performance
activityLogSchema.index({ user: 1, createdAt: -1 });
activityLogSchema.index({ action: 1, createdAt: -1 });
activityLogSchema.index({ targetType: 1, targetId: 1 });
activityLogSchema.index({ severity: 1, createdAt: -1 });
activityLogSchema.index({ isSuccessful: 1 });
activityLogSchema.index({ createdAt: -1 });

// Static method to log activity
activityLogSchema.statics.logActivity = async function(logData) {
  try {
    const {
      userId,
      action,
      description,
      targetType = null,
      targetId = null,
      metadata = {},
      ipAddress = null,
      userAgent = null,
      location = null,
      severity = 'info',
      isSuccessful = true,
      errorMessage = null,
      duration = null
    } = logData;

    const activityLog = new this({
      user: userId,
      action,
      description,
      targetType,
      targetId,
      metadata,
      ipAddress,
      userAgent,
      location,
      severity,
      isSuccessful,
      errorMessage,
      duration
    });

    await activityLog.save();
    return activityLog;
  } catch (error) {
    console.error('Error logging activity:', error);
    throw error;
  }
};

// Static method to get user activity history
activityLogSchema.statics.getUserActivity = function(userId, options = {}) {
  const {
    actions = null,
    targetType = null,
    severity = null,
    isSuccessful = null,
    startDate = null,
    endDate = null,
    limit = 50,
    skip = 0,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  const query = { user: userId };

  if (actions && actions.length > 0) {
    query.action = { $in: actions };
  }

  if (targetType) {
    query.targetType = targetType;
  }

  if (severity) {
    query.severity = severity;
  }

  if (isSuccessful !== null) {
    query.isSuccessful = isSuccessful;
  }

  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .populate('user', 'firstName lastName email role')
    .sort({ [sortBy]: sortOrder })
    .limit(limit)
    .skip(skip);
};

// Static method to get activity statistics
activityLogSchema.statics.getActivityStats = async function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const pipeline = [
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          action: '$action',
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.action',
        totalCount: { $sum: '$count' },
        dailyActivity: {
          $push: {
            date: '$_id.date',
            count: '$count'
          }
        }
      }
    },
    {
      $sort: { totalCount: -1 }
    }
  ];

  return await this.aggregate(pipeline);
};

// Static method to get system-wide activity for admins
activityLogSchema.statics.getSystemActivity = function(options = {}) {
  const {
    actions = null,
    severity = null,
    isSuccessful = null,
    startDate = null,
    endDate = null,
    limit = 100,
    skip = 0
  } = options;

  const query = {};

  if (actions && actions.length > 0) {
    query.action = { $in: actions };
  }

  if (severity) {
    query.severity = severity;
  }

  if (isSuccessful !== null) {
    query.isSuccessful = isSuccessful;
  }

  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .populate('user', 'firstName lastName email role')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

module.exports = mongoose.model('ActivityLog', activityLogSchema);
