const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('./models/User');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const createSuperUser = async () => {
  try {
    await connectDB();

    // Get user input or use defaults
    const email = process.argv[2] || '<EMAIL>';
    const password = process.argv[3] || 'SuperAdmin123!';
    const fullName = process.argv[4] || 'Super Administrator';

    console.log('🚀 Creating Super User...');
    console.log('📧 Email:', email);
    console.log('👤 Name:', fullName);

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    
    if (existingUser) {
      console.log('⚠️  User already exists! Updating to super_admin...');
      
      // Update existing user to super_admin
      existingUser.role = 'super_admin';
      existingUser.status = 'approved';
      existingUser.emailVerified = true;
      
      // Update profile if provided
      if (!existingUser.profile) {
        existingUser.profile = {};
      }
      existingUser.profile.fullName = fullName;
      
      await existingUser.save();
      
      console.log('✅ User updated to super_admin successfully!');
      console.log('📧 Email:', email);
      console.log('🔑 Password: (unchanged)');
      console.log('👤 Role:', existingUser.role);
      console.log('✅ Status:', existingUser.status);
      console.log('📱 Email Verified:', existingUser.emailVerified);
      
    } else {
      // Create new super user (password will be hashed by pre-save middleware)
      const superUser = new User({
        email: email,
        password: password,
        role: 'super_admin',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: fullName,
          phone: '+966501234567',
          address: 'Admin Office, Riyadh, Saudi Arabia',
          preferredCurrency: 'SAR',
          country: 'SA',
          timezone: 'Asia/Riyadh'
        },
        preferences: {
          emailNotifications: true,
          smsNotifications: true,
          inAppNotifications: true,
          notificationTypes: {
            bidPlaced: true,
            bidOutbid: true,
            auctionEnding: true,
            auctionWon: true,
            paymentRequired: true,
            systemAnnouncements: true
          }
        },
        createdAt: new Date(),
        lastLogin: new Date()
      });

      const savedUser = await superUser.save();
      
      console.log('✅ Super User created successfully!');
      console.log('📧 Email:', email);
      console.log('🔑 Password:', password);
      console.log('👤 Role:', savedUser.role);
      console.log('✅ Status:', savedUser.status);
      console.log('📱 Email Verified:', savedUser.emailVerified);
    }

    console.log('\n🎯 Super User Permissions:');
    console.log('• manage_admins - Can create and manage other admins');
    console.log('• manage_users - Full user management');
    console.log('• manage_auctions - Complete auction control');
    console.log('• manage_tenders - Complete tender control');
    console.log('• view_all_reports - Access all system reports');
    console.log('• system_settings - Modify system configurations');

    console.log('\n🌐 Access URLs:');
    console.log('• Admin Dashboard: http://localhost:3000/admin/dashboard');
    console.log('• User Management: http://localhost:3000/admin/users');
    console.log('• Auction Management: http://localhost:3000/admin/auctions');
    console.log('• Tender Management: http://localhost:3000/admin/tenders');

    console.log('\n🚀 You can now log in with these credentials!');
    
  } catch (error) {
    if (error.code === 11000) {
      console.log('❌ User already exists with this email');
    } else {
      console.error('❌ Error creating super user:', error);
    }
  } finally {
    mongoose.connection.close();
    process.exit(0);
  }
};

// Show usage if no arguments provided
if (process.argv.length === 2) {
  console.log('🔧 Super User Creation Script');
  console.log('');
  console.log('Usage:');
  console.log('  node create-super-user.js [email] [password] [fullName]');
  console.log('');
  console.log('Examples:');
  console.log('  node create-super-user.js');
  console.log('  node create-super-user.js <EMAIL> MyPassword123 "Admin User"');
  console.log('');
  console.log('Default values:');
  console.log('  Email: <EMAIL>');
  console.log('  Password: SuperAdmin123!');
  console.log('  Name: Super Administrator');
  console.log('');
}

createSuperUser();
