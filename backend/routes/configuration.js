const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate, requireRole } = require('../middleware/auth');
const ActivityLog = require('../models/ActivityLog');

const router = express.Router();

// Apply authentication and super admin role to all routes
router.use(authenticate);
router.use(requireRole('super_admin'));

// Mock configuration storage (in production, use database or config files)
let platformConfig = {
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 27017,
    name: process.env.DB_NAME || 'auction_platform',
    username: process.env.DB_USER || 'admin',
    password: '••••••••', // Never expose real password
    ssl: process.env.DB_SSL === 'true',
    connectionPool: {
      min: 5,
      max: 20,
      idle: 10000
    }
  },
  email: {
    provider: process.env.EMAIL_PROVIDER || 'smtp',
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      username: process.env.SMTP_USER || '<EMAIL>',
      password: '••••••••'
    },
    sendgrid: {
      apiKey: '••••••••'
    },
    templates: {
      welcome: 'welcome-template-id',
      passwordReset: 'password-reset-template-id',
      auctionWon: 'auction-won-template-id',
      bidOutbid: 'bid-outbid-template-id'
    }
  },
  payment: {
    stripe: {
      enabled: process.env.STRIPE_ENABLED === 'true',
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY ? 'pk_test_••••••••' : '',
      secretKey: '••••••••',
      webhookSecret: '••••••••'
    },
    paypal: {
      enabled: process.env.PAYPAL_ENABLED === 'true',
      clientId: '••••••••',
      clientSecret: '••••••••',
      sandbox: process.env.PAYPAL_SANDBOX === 'true'
    },
    mada: {
      enabled: process.env.MADA_ENABLED === 'true',
      merchantId: process.env.MADA_MERCHANT_ID ? 'MADA_••••••••' : '',
      apiKey: '••••••••'
    },
    stcPay: {
      enabled: process.env.STC_PAY_ENABLED === 'true',
      merchantId: process.env.STC_PAY_MERCHANT_ID ? 'STC_••••••••' : '',
      apiKey: '••••••••'
    }
  },
  system: {
    environment: process.env.NODE_ENV || 'development',
    debug: process.env.DEBUG === 'true',
    logLevel: process.env.LOG_LEVEL || 'info',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760, // 10MB
    allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
    rateLimit: {
      windowMs: 900000, // 15 minutes
      maxRequests: 100
    },
    cache: {
      enabled: process.env.CACHE_ENABLED === 'true',
      ttl: parseInt(process.env.CACHE_TTL) || 3600,
      provider: process.env.CACHE_PROVIDER || 'redis',
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: '••••••••'
      }
    }
  },
  integrations: {
    cloudinary: {
      enabled: process.env.CLOUDINARY_ENABLED === 'true',
      cloudName: process.env.CLOUDINARY_CLOUD_NAME || 'auction-platform',
      apiKey: '••••••••',
      apiSecret: '••••••••'
    },
    sms: {
      provider: process.env.SMS_PROVIDER || 'unifonic',
      twilio: {
        accountSid: '••••••••',
        authToken: '••••••••',
        fromNumber: process.env.TWILIO_FROM_NUMBER || '+**********'
      },
      unifonic: {
        appSid: process.env.UNIFONIC_APP_SID ? '••••••••' : '',
        senderId: process.env.UNIFONIC_SENDER_ID || 'AUCTION'
      }
    },
    analytics: {
      googleAnalytics: {
        enabled: process.env.GA_ENABLED === 'true',
        trackingId: process.env.GA_TRACKING_ID ? 'GA-••••••••' : ''
      },
      mixpanel: {
        enabled: process.env.MIXPANEL_ENABLED === 'true',
        projectToken: '••••••••'
      }
    }
  }
};

// @route   GET /api/configuration
// @desc    Get platform configuration
// @access  Super Admin Only
router.get('/', async (req, res) => {
  try {
    res.json({
      success: true,
      data: platformConfig
    });
  } catch (error) {
    console.error('Get configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching configuration'
    });
  }
});

// @route   PUT /api/configuration
// @desc    Update platform configuration
// @access  Super Admin Only
router.put('/', [
  body('database.host').optional().isString(),
  body('database.port').optional().isInt({ min: 1, max: 65535 }),
  body('email.provider').optional().isIn(['smtp', 'sendgrid', 'mailgun', 'ses']),
  body('system.environment').optional().isIn(['development', 'staging', 'production']),
  body('system.logLevel').optional().isIn(['error', 'warn', 'info', 'debug'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Update configuration (deep merge)
    const updates = req.body;
    platformConfig = mergeDeep(platformConfig, updates);

    // Log the action
    await ActivityLog.create({
      user: req.user.id,
      action: 'configuration_update',
      category: 'system',
      severity: 'info',
      description: 'Platform configuration updated by super admin',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: {
        updatedSections: Object.keys(updates),
        timestamp: new Date().toISOString()
      }
    });

    res.json({
      success: true,
      message: 'Configuration updated successfully',
      data: platformConfig
    });
  } catch (error) {
    console.error('Update configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating configuration'
    });
  }
});

// @route   POST /api/configuration/test-connection
// @desc    Test service connections
// @access  Super Admin Only
router.post('/test-connection', async (req, res) => {
  try {
    const { service } = req.body;
    
    // Mock connection testing
    const testResults = {
      database: Math.random() > 0.05, // 95% success rate
      email: Math.random() > 0.1,     // 90% success rate
      stripe: Math.random() > 0.1,    // 90% success rate
      mada: Math.random() > 0.15,     // 85% success rate
      stcpay: Math.random() > 0.15,   // 85% success rate
      cloudinary: Math.random() > 0.1, // 90% success rate
      redis: Math.random() > 0.05     // 95% success rate
    };

    const result = testResults[service] !== undefined ? testResults[service] : false;
    const responseTime = Math.floor(Math.random() * 1000) + 100; // 100-1100ms

    // Log the test
    await ActivityLog.create({
      user: req.user.id,
      action: 'system_test', // Using a valid enum value
      category: 'system',
      severity: result ? 'info' : 'warning',
      description: `Connection test for ${service}: ${result ? 'SUCCESS' : 'FAILED'} (${responseTime}ms)`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { service, result, responseTime }
    });

    res.json({
      success: true,
      data: {
        service,
        connected: result,
        responseTime,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Connection test error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while testing connection'
    });
  }
});

// @route   GET /api/configuration/export
// @desc    Export configuration as JSON
// @access  Super Admin Only
router.get('/export', async (req, res) => {
  try {
    // Create sanitized config for export (remove sensitive data)
    const exportConfig = JSON.parse(JSON.stringify(platformConfig));
    
    // Remove sensitive fields
    if (exportConfig.database) exportConfig.database.password = '[REDACTED]';
    if (exportConfig.email?.smtp) exportConfig.email.smtp.password = '[REDACTED]';
    if (exportConfig.email?.sendgrid) exportConfig.email.sendgrid.apiKey = '[REDACTED]';
    if (exportConfig.payment?.stripe) {
      exportConfig.payment.stripe.secretKey = '[REDACTED]';
      exportConfig.payment.stripe.webhookSecret = '[REDACTED]';
    }
    // ... redact other sensitive fields

    // Log the export
    await ActivityLog.create({
      user: req.user.id,
      action: 'file_export', // Using a valid enum value
      category: 'system',
      severity: 'info',
      description: 'Platform configuration exported by super admin',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="platform-config-${new Date().toISOString().split('T')[0]}.json"`);
    res.json({
      success: true,
      data: exportConfig,
      exportedAt: new Date().toISOString(),
      exportedBy: req.user.email
    });
  } catch (error) {
    console.error('Export configuration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while exporting configuration'
    });
  }
});

// Helper function for deep merging objects
function mergeDeep(target, source) {
  const output = Object.assign({}, target);
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target))
          Object.assign(output, { [key]: source[key] });
        else
          output[key] = mergeDeep(target[key], source[key]);
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  return output;
}

function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item);
}

module.exports = router;