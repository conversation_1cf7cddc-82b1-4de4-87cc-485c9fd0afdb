const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');
const ActivityLog = require('../models/ActivityLog');
const { authenticate, requireRole } = require('../middleware/auth');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// Apply authentication and super admin role to all routes
router.use(authenticate);
router.use(requireRole('super_admin'));

// ==================== ANALYTICS ====================

// @route   GET /api/superadmin/analytics
// @desc    Get comprehensive analytics for super admin
// @access  Super Admin Only
router.get('/analytics', async (req, res) => {
  try {
    const { timeRange = '30' } = req.query;
    const days = parseInt(timeRange);
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Get comprehensive analytics data
    const [
      userStats,
      auctionStats,
      tenderStats,
      revenueStats,
      activityStats
    ] = await Promise.all([
      // User analytics
      User.aggregate([
        {
          $facet: {
            total: [{ $count: "count" }],
            byRole: [
              { $group: { _id: "$role", count: { $sum: 1 } } }
            ],
            byStatus: [
              { $group: { _id: "$status", count: { $sum: 1 } } }
            ],
            recent: [
              { $match: { createdAt: { $gte: startDate } } },
              { $count: "count" }
            ],
            growth: [
              {
                $match: { createdAt: { $gte: startDate } }
              },
              {
                $group: {
                  _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
                  count: { $sum: 1 }
                }
              },
              { $sort: { _id: 1 } }
            ]
          }
        }
      ]),

      // Auction analytics
      Auction.aggregate([
        {
          $facet: {
            total: [{ $count: "count" }],
            active: [
              { $match: { status: "active" } },
              { $count: "count" }
            ],
            completed: [
              { $match: { status: "completed" } },
              { $count: "count" }
            ],
            recent: [
              { $match: { createdAt: { $gte: startDate } } },
              { $count: "count" }
            ]
          }
        }
      ]),

      // Tender analytics
      Tender.aggregate([
        {
          $facet: {
            total: [{ $count: "count" }],
            open: [
              { $match: { status: "open" } },
              { $count: "count" }
            ],
            closed: [
              { $match: { status: "closed" } },
              { $count: "count" }
            ],
            recent: [
              { $match: { createdAt: { $gte: startDate } } },
              { $count: "count" }
            ]
          }
        }
      ]),

      // Revenue analytics (mock data for now)
      Promise.resolve({
        totalRevenue: Math.floor(Math.random() * 1000000) + 500000,
        monthlyRevenue: Math.floor(Math.random() * 100000) + 50000,
        growth: Math.floor(Math.random() * 20) + 5
      }),

      // Activity analytics
      ActivityLog.aggregate([
        {
          $match: { timestamp: { $gte: startDate } }
        },
        {
          $facet: {
            total: [{ $count: "count" }],
            byCategory: [
              { $group: { _id: "$category", count: { $sum: 1 } } }
            ],
            bySeverity: [
              { $group: { _id: "$severity", count: { $sum: 1 } } }
            ]
          }
        }
      ])
    ]);

    const analytics = {
      overview: {
        users: {
          total: userStats[0]?.total[0]?.count || 0,
          recent: userStats[0]?.recent[0]?.count || 0,
          byRole: userStats[0]?.byRole || [],
          byStatus: userStats[0]?.byStatus || [],
          growth: userStats[0]?.growth || []
        },
        auctions: {
          total: auctionStats[0]?.total[0]?.count || 0,
          active: auctionStats[0]?.active[0]?.count || 0,
          completed: auctionStats[0]?.completed[0]?.count || 0,
          recent: auctionStats[0]?.recent[0]?.count || 0
        },
        tenders: {
          total: tenderStats[0]?.total[0]?.count || 0,
          open: tenderStats[0]?.open[0]?.count || 0,
          closed: tenderStats[0]?.closed[0]?.count || 0,
          recent: tenderStats[0]?.recent[0]?.count || 0
        },
        revenue: revenueStats,
        activity: {
          total: activityStats[0]?.total[0]?.count || 0,
          byCategory: activityStats[0]?.byCategory || [],
          bySeverity: activityStats[0]?.bySeverity || []
        }
      },
      timeRange: days,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics'
    });
  }
});

// ==================== SYSTEM LOGS & AUDIT TRAIL ====================

// @route   GET /api/superadmin/logs
// @desc    Get system logs and audit trail
// @access  Super Admin Only
router.get('/logs', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const skip = (page - 1) * limit;
    const { category, level, search, timeRange } = req.query;

    // Build filter object
    const filter = {};
    if (category && category !== 'all') filter.category = category;
    if (level && level !== 'all') filter.severity = level;
    if (search) {
      filter.$or = [
        { action: { $regex: search, $options: 'i' } },
        { details: { $regex: search, $options: 'i' } },
        { 'user.email': { $regex: search, $options: 'i' } }
      ];
    }

    // Time range filter
    if (timeRange) {
      const hours = parseInt(timeRange);
      if (hours > 0) {
        filter.createdAt = {
          $gte: new Date(Date.now() - hours * 60 * 60 * 1000)
        };
      }
    }

    const logs = await ActivityLog.find(filter)
      .populate('user', 'email profile.fullName role')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await ActivityLog.countDocuments(filter);

    // Get statistics
    const stats = await ActivityLog.aggregate([
      {
        $group: {
          _id: null,
          totalLogs: { $sum: 1 },
          errorCount: {
            $sum: { $cond: [{ $eq: ['$severity', 'error'] }, 1, 0] }
          },
          warningCount: {
            $sum: { $cond: [{ $eq: ['$severity', 'warning'] }, 1, 0] }
          },
          securityEvents: {
            $sum: { $cond: [{ $eq: ['$category', 'security'] }, 1, 0] }
          },
          adminActions: {
            $sum: { $cond: [{ $eq: ['$category', 'admin'] }, 1, 0] }
          },
          userActions: {
            $sum: { $cond: [{ $eq: ['$category', 'user'] }, 1, 0] }
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        logs: logs.map(log => ({
          id: log._id,
          timestamp: log.createdAt,
          level: log.severity || 'info',
          category: log.category || 'system',
          action: log.action,
          user: log.user ? {
            id: log.user._id,
            email: log.user.email,
            role: log.user.role
          } : null,
          details: log.description,
          ipAddress: log.ipAddress || 'Unknown',
          userAgent: log.userAgent,
          metadata: log.metadata
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        stats: stats[0] || {
          totalLogs: 0,
          errorCount: 0,
          warningCount: 0,
          securityEvents: 0,
          adminActions: 0,
          userActions: 0
        }
      }
    });
  } catch (error) {
    console.error('System logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching system logs'
    });
  }
});

// ==================== ADMIN MANAGEMENT ====================

// @route   GET /api/superadmin/admins
// @desc    Get all admin users
// @access  Super Admin Only
router.get('/admins', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const { role, status, search } = req.query;

    // Build filter for admin users only
    const filter = {
      role: { $in: ['admin', 'super_admin'] }
    };

    if (role && role !== 'all') filter.role = role;
    if (status && status !== 'all') filter.status = status;
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { 'profile.fullName': { $regex: search, $options: 'i' } }
      ];
    }

    const admins = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(filter);

    // Get admin statistics
    const stats = await User.aggregate([
      { $match: { role: { $in: ['admin', 'super_admin'] } } },
      {
        $group: {
          _id: null,
          totalAdmins: { $sum: 1 },
          activeAdmins: {
            $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] }
          },
          suspendedAdmins: {
            $sum: { $cond: [{ $eq: ['$status', 'suspended'] }, 1, 0] }
          },
          superAdmins: {
            $sum: { $cond: [{ $eq: ['$role', 'super_admin'] }, 1, 0] }
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        admins: admins.map(admin => {
          // Get permissions based on role
          const rolePermissions = {
            super_admin: [
              'manage_admins',
              'manage_users',
              'manage_auctions',
              'manage_tenders',
              'system_settings',
              'view_all_reports',
              'manage_security',
              'system_health'
            ],
            admin: [
              'manage_users',
              'manage_auctions',
              'manage_tenders',
              'view_reports'
            ]
          };

          return {
            id: admin._id,
            email: admin.email,
            role: admin.role,
            status: admin.status,
            profile: {
              ...admin.profile,
              department: admin.profile?.department || (admin.role === 'super_admin' ? 'System Administration' : 'Operations')
            },
            permissions: rolePermissions[admin.role] || [],
            createdAt: admin.createdAt,
            lastLogin: admin.lastLogin,
            createdBy: admin.createdBy
          };
        }),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        stats: stats[0] || {
          totalAdmins: 0,
          activeAdmins: 0,
          suspendedAdmins: 0,
          superAdmins: 0
        }
      }
    });
  } catch (error) {
    console.error('Admin management error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching admin users'
    });
  }
});

// @route   POST /api/superadmin/admins
// @desc    Create new admin user
// @access  Super Admin Only
router.post('/admins', [
  body('email').isEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('fullName').notEmpty().withMessage('Full name is required'),
  body('role').isIn(['admin', 'super_admin']).withMessage('Valid role is required'),
  body('department').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { email, password, fullName, role, department, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new admin user
    const newAdmin = new User({
      email,
      password: hashedPassword,
      role,
      status: 'approved',
      emailVerified: true,
      profile: {
        fullName,
        phone: phone || '',
        department: department || '',
        preferredCurrency: 'SAR',
        country: 'SA',
        timezone: 'Asia/Riyadh'
      },
      permissions: role === 'super_admin' ? [
        'manage_admins',
        'manage_users',
        'manage_auctions',
        'manage_tenders',
        'system_settings',
        'view_all_reports',
        'manage_security',
        'system_health'
      ] : [
        'manage_users',
        'manage_auctions',
        'manage_tenders',
        'view_reports'
      ],
      createdBy: req.user.id,
      createdAt: new Date()
    });

    await newAdmin.save();

    // Log the action
    await ActivityLog.create({
      user: req.user.id,
      action: 'admin_created',
      category: 'admin',
      severity: 'info',
      description: `Created new ${role} user: ${email}`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: {
        targetUserId: newAdmin._id,
        targetEmail: email,
        role
      }
    });

    res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      data: {
        admin: {
          id: newAdmin._id,
          email: newAdmin.email,
          role: newAdmin.role,
          status: newAdmin.status,
          profile: newAdmin.profile
        }
      }
    });
  } catch (error) {
    console.error('Create admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating admin user'
    });
  }
});

// @route   PUT /api/superadmin/admins/:id
// @desc    Update admin user
// @access  Super Admin Only
router.put('/admins/:id', [
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('fullName').optional().notEmpty().withMessage('Full name cannot be empty'),
  body('role').optional().isIn(['admin', 'super_admin']).withMessage('Valid role is required'),
  body('status').optional().isIn(['approved', 'suspended']).withMessage('Valid status is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updates = req.body;

    // Prevent self-modification of critical fields
    if (id === req.user.id && (updates.role || updates.status)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot modify your own role or status'
      });
    }

    const admin = await User.findById(id);
    if (!admin || !['admin', 'super_admin'].includes(admin.role)) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }

    // Update fields
    if (updates.email) admin.email = updates.email;
    if (updates.role) admin.role = updates.role;
    if (updates.status) admin.status = updates.status;
    if (updates.fullName) admin.profile.fullName = updates.fullName;
    if (updates.department) admin.profile.department = updates.department;
    if (updates.phone) admin.profile.phone = updates.phone;

    await admin.save();

    // Log the action
    await ActivityLog.create({
      user: req.user.id,
      action: 'admin_updated',
      category: 'admin',
      severity: 'info',
      description: `Updated admin user: ${admin.email}`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: {
        targetUserId: admin._id,
        targetEmail: admin.email,
        updates
      }
    });

    res.json({
      success: true,
      message: 'Admin user updated successfully',
      data: {
        admin: {
          id: admin._id,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          profile: admin.profile
        }
      }
    });
  } catch (error) {
    console.error('Update admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating admin user'
    });
  }
});

// @route   DELETE /api/superadmin/admins/:id
// @desc    Delete admin user
// @access  Super Admin Only
router.delete('/admins/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent self-deletion
    if (id === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account'
      });
    }

    const admin = await User.findById(id);
    if (!admin || !['admin', 'super_admin'].includes(admin.role)) {
      return res.status(404).json({
        success: false,
        message: 'Admin user not found'
      });
    }

    // Prevent deletion of super admins by other super admins (optional business rule)
    if (admin.role === 'super_admin') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete super admin accounts'
      });
    }

    await User.findByIdAndDelete(id);

    // Log the action
    await ActivityLog.create({
      user: req.user.id,
      action: 'admin_deleted',
      category: 'admin',
      severity: 'warning',
      description: `Deleted admin user: ${admin.email}`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: {
        targetUserId: admin._id,
        targetEmail: admin.email,
        targetRole: admin.role
      }
    });

    res.json({
      success: true,
      message: 'Admin user deleted successfully'
    });
  } catch (error) {
    console.error('Delete admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting admin user'
    });
  }
});

// ==================== SYSTEM HEALTH MONITORING ====================

// @route   GET /api/superadmin/system-health
// @desc    Get system health and performance metrics
// @access  Super Admin Only
router.get('/system-health', async (req, res) => {
  try {
    // Mock system health data - replace with actual system monitoring
    const systemHealth = {
      overall: 'healthy',
      uptime: Math.floor(process.uptime()),
      lastUpdated: new Date().toISOString(),
      services: {
        api: {
          status: 'online',
          responseTime: Math.floor(Math.random() * 200) + 50,
          uptime: 99.9,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 0.5
        },
        database: {
          status: 'online',
          responseTime: Math.floor(Math.random() * 50) + 10,
          uptime: 99.8,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 0.3
        },
        cache: {
          status: 'online',
          responseTime: Math.floor(Math.random() * 10) + 2,
          uptime: 99.95,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 0.1
        },
        email: {
          status: Math.random() > 0.8 ? 'degraded' : 'online',
          responseTime: Math.floor(Math.random() * 3000) + 500,
          uptime: 98.5,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 2
        },
        payment: {
          status: 'online',
          responseTime: Math.floor(Math.random() * 1000) + 500,
          uptime: 99.7,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 0.5
        },
        storage: {
          status: 'online',
          responseTime: Math.floor(Math.random() * 100) + 30,
          uptime: 99.9,
          lastCheck: new Date().toISOString(),
          errorRate: Math.random() * 0.2
        }
      },
      performance: {
        cpu: {
          current: Math.random() * 80 + 10,
          average: Math.random() * 60 + 20,
          peak: Math.random() * 20 + 80,
          trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)]
        },
        memory: {
          current: Math.random() * 40 + 50,
          average: Math.random() * 30 + 50,
          peak: Math.random() * 20 + 70,
          trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)]
        },
        disk: {
          current: Math.random() * 30 + 20,
          average: Math.random() * 20 + 25,
          peak: Math.random() * 20 + 40,
          trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)]
        },
        network: {
          current: Math.random() * 50 + 10,
          average: Math.random() * 30 + 15,
          peak: Math.random() * 40 + 50,
          trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)]
        }
      },
      alerts: []
    };

    // Get recent system alerts from activity logs
    const recentAlerts = await ActivityLog.find({
      category: { $in: ['system', 'security'] },
      level: { $in: ['warning', 'error'] },
      timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    })
    .sort({ timestamp: -1 })
    .limit(10);

    systemHealth.alerts = recentAlerts.map(alert => ({
      id: alert._id,
      type: alert.level === 'error' ? 'critical' : 'warning',
      title: alert.action,
      description: alert.details,
      timestamp: alert.timestamp,
      resolved: false,
      service: alert.category
    }));

    res.json({
      success: true,
      data: systemHealth
    });
  } catch (error) {
    console.error('System health error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching system health'
    });
  }
});

module.exports = router;