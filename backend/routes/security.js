const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate, requireRole } = require('../middleware/auth');
const ActivityLog = require('../models/ActivityLog');

const router = express.Router();

// Apply authentication and super admin role to all routes
router.use(authenticate);
router.use(requireRole('super_admin'));

// Mock security settings storage (in production, use database)
let securitySettings = {
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAge: 90,
    preventReuse: 5
  },
  sessionSettings: {
    maxDuration: 480, // 8 hours
    idleTimeout: 30,
    maxConcurrentSessions: 3,
    requireReauth: true
  },
  accessControl: {
    enableIPRestrictions: true,
    allowedIPs: ['***********/24', '10.0.0.0/8'],
    blockedIPs: ['************', '************/24'],
    enableGeoBlocking: false,
    allowedCountries: ['SA', 'AE', 'KW'],
    blockedCountries: ['CN', 'RU']
  },
  securityMonitoring: {
    enableFailedLoginTracking: true,
    maxFailedAttempts: 5,
    lockoutDuration: 15,
    enableSuspiciousActivityDetection: true,
    enableRealTimeAlerts: true
  },
  twoFactorAuth: {
    enforceForAdmins: true,
    enforceForUsers: false,
    allowedMethods: ['totp', 'sms', 'email']
  }
};

// @route   GET /api/security/settings
// @desc    Get security settings
// @access  Super Admin Only
router.get('/settings', async (req, res) => {
  try {
    res.json({
      success: true,
      data: securitySettings
    });
  } catch (error) {
    console.error('Get security settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching security settings'
    });
  }
});

// @route   PUT /api/security/settings
// @desc    Update security settings
// @access  Super Admin Only
router.put('/settings', [
  body('passwordPolicy.minLength').optional().isInt({ min: 6, max: 32 }),
  body('passwordPolicy.maxAge').optional().isInt({ min: 30, max: 365 }),
  body('sessionSettings.maxDuration').optional().isInt({ min: 60, max: 1440 }),
  body('sessionSettings.idleTimeout').optional().isInt({ min: 5, max: 120 }),
  body('securityMonitoring.maxFailedAttempts').optional().isInt({ min: 3, max: 10 }),
  body('securityMonitoring.lockoutDuration').optional().isInt({ min: 5, max: 60 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Update security settings
    const updates = req.body;
    securitySettings = { ...securitySettings, ...updates };

    // Log the action
    await ActivityLog.create({
      user: req.user.id,
      action: 'profile_update', // Using a valid enum value
      category: 'security',
      severity: 'info',
      description: 'Security settings updated by super admin',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { updates }
    });

    res.json({
      success: true,
      message: 'Security settings updated successfully',
      data: securitySettings
    });
  } catch (error) {
    console.error('Update security settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating security settings'
    });
  }
});

// @route   POST /api/security/test-connection
// @desc    Test security service connections
// @access  Super Admin Only
router.post('/test-connection', async (req, res) => {
  try {
    const { service } = req.body;
    
    // Mock connection testing
    const testResults = {
      database: Math.random() > 0.1,
      email: Math.random() > 0.2,
      sms: Math.random() > 0.15,
      twoFactor: Math.random() > 0.1
    };

    const result = testResults[service] !== undefined ? testResults[service] : false;

    // Log the test
    await ActivityLog.create({
      user: req.user.id,
      action: 'security_connection_test',
      category: 'security',
      level: result ? 'info' : 'warning',
      details: `Security connection test for ${service}: ${result ? 'SUCCESS' : 'FAILED'}`,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      metadata: { service, result }
    });

    res.json({
      success: true,
      data: {
        service,
        connected: result,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Security connection test error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while testing security connection'
    });
  }
});

// @route   GET /api/security/alerts
// @desc    Get security alerts
// @access  Super Admin Only
router.get('/alerts', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const alerts = await ActivityLog.find({
      category: 'security',
      level: { $in: ['warning', 'error'] }
    })
    .populate('user', 'email profile.fullName')
    .sort({ timestamp: -1 })
    .skip(skip)
    .limit(limit);

    const total = await ActivityLog.countDocuments({
      category: 'security',
      level: { $in: ['warning', 'error'] }
    });

    res.json({
      success: true,
      data: {
        alerts: alerts.map(alert => ({
          id: alert._id,
          type: alert.level === 'error' ? 'critical' : 'warning',
          title: alert.action,
          description: alert.details,
          timestamp: alert.timestamp,
          resolved: false,
          user: alert.user
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Security alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching security alerts'
    });
  }
});

module.exports = router;