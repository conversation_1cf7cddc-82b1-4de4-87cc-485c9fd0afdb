const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');
const { authenticate, requireRole, authorize } = require('../middleware/auth');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// Apply authentication to all admin routes
router.use(authenticate);

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard statistics
// @access  Admin Only
router.get('/dashboard',
  requireRole('admin'),
  async (req, res) => {
    try {
      const [
        totalUsers,
        pendingApprovals,
        activeAuctions,
        activeTenders,
        recentRegistrations
      ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ status: { $in: ['documents_submitted', 'under_review'] } }),
        Auction.countDocuments({ status: 'active' }),
        Tender.countDocuments({ status: 'open' }),
        User.countDocuments({ 
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        })
      ]);

      const usersByRole = await User.aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } }
      ]);

      const usersByStatus = await User.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      res.json({
        success: true,
        data: {
          stats: {
            totalUsers,
            pendingApprovals,
            activeAuctions,
            activeTenders,
            recentRegistrations
          },
          usersByRole: usersByRole.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
          usersByStatus: usersByStatus.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {})
        }
      });
    } catch (error) {
      console.error('Dashboard error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching dashboard data'
      });
    }
  }
);

// @route   GET /api/admin/pending-accounts
// @desc    Get accounts pending approval
// @access  Admin Only
router.get('/pending-accounts',
  requireRole('admin'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      const accounts = await User.find({
        status: { $in: ['documents_submitted', 'under_review', 'pending_email_verification'] }
      })
      .select('-password')
      .sort({ submittedAt: 1 })
      .skip(skip)
      .limit(limit);

      const total = await User.countDocuments({
        status: { $in: ['documents_submitted', 'under_review', 'pending_email_verification'] }
      });

      res.json({
        success: true,
        data: {
          accounts,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Pending accounts error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching pending accounts'
      });
    }
  }
);

// @route   GET /api/admin/account/:id
// @desc    Get detailed account information
// @access  Admin Only
router.get('/account/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const account = await User.findById(req.params.id)
        .select('-password')
        .populate('reviewedBy', 'profile.fullName email');

      if (!account) {
        return res.status(404).json({
          success: false,
          message: 'Account not found'
        });
      }

      res.json({
        success: true,
        data: { account }
      });
    } catch (error) {
      console.error('Account details error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching account details'
      });
    }
  }
);

// @route   POST /api/admin/approve-account/:id
// @desc    Approve a pending account
// @access  Admin Only
router.post('/approve-account/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const account = await User.findById(req.params.id);

      if (!account) {
        return res.status(404).json({
          success: false,
          message: 'Account not found'
        });
      }

      if (account.status !== 'documents_submitted' && account.status !== 'under_review') {
        return res.status(400).json({
          success: false,
          message: 'Account is not pending approval'
        });
      }

      account.status = 'approved';
      account.reviewedAt = new Date();
      account.reviewedBy = req.user._id;
      account.rejectionReason = undefined;

      await account.save();

      // Send approval email
      await sendEmail({
        to: account.email,
        subject: 'تم تفعيل حسابك - منصة المزادات والمناقصات',
        html: `
          <h2>مبروك! تم تفعيل حسابك</h2>
          <p>عزيزي ${account.profile.fullName || account.profile.companyName || account.profile.governmentEntity},</p>
          <p>تم مراجعة وثائقك وتفعيل حسابك بنجاح.</p>
          <p>يمكنك الآن تسجيل الدخول والاستفادة من جميع خدمات المنصة.</p>
          <a href="${process.env.CLIENT_URL}/auth/login" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">تسجيل الدخول</a>
          <p>شكراً لاختيارك منصة المزادات والمناقصات.</p>
        `
      });

      res.json({
        success: true,
        message: 'Account approved successfully',
        data: {
          account: {
            id: account._id,
            email: account.email,
            status: account.status,
            reviewedAt: account.reviewedAt
          }
        }
      });
    } catch (error) {
      console.error('Approve account error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while approving account'
      });
    }
  }
);

// @route   POST /api/admin/reject-account/:id
// @desc    Reject a pending account
// @access  Admin Only
router.post('/reject-account/:id',
  [
    body('reason').notEmpty().withMessage('Rejection reason is required')
  ],
  requireRole('admin'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { reason } = req.body;
      const account = await User.findById(req.params.id);

      if (!account) {
        return res.status(404).json({
          success: false,
          message: 'Account not found'
        });
      }

      if (account.status !== 'documents_submitted' && account.status !== 'under_review') {
        return res.status(400).json({
          success: false,
          message: 'Account is not pending approval'
        });
      }

      account.status = 'rejected';
      account.reviewedAt = new Date();
      account.reviewedBy = req.user._id;
      account.rejectionReason = reason;

      await account.save();

      // Send rejection email
      await sendEmail({
        to: account.email,
        subject: 'تحديث حالة حسابك - منصة المزادات والمناقصات',
        html: `
          <h2>تحديث حالة حسابك</h2>
          <p>عزيزي ${account.profile.fullName || account.profile.companyName || account.profile.governmentEntity},</p>
          <p>نأسف لإعلامك أنه تم رفض طلب تفعيل حسابك للأسباب التالية:</p>
          <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <p style="color: #dc2626; margin: 0;"><strong>سبب الرفض:</strong> ${reason}</p>
          </div>
          <p>يمكنك تصحيح الأخطاء المذكورة وإعادة تقديم الطلب.</p>
          <a href="${process.env.CLIENT_URL}/auth/register" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">إعادة التسجيل</a>
          <p>إذا كان لديك أي استفسارات، يرجى التواصل مع دعم العملاء.</p>
        `
      });

      res.json({
        success: true,
        message: 'Account rejected successfully',
        data: {
          account: {
            id: account._id,
            email: account.email,
            status: account.status,
            rejectionReason: account.rejectionReason,
            reviewedAt: account.reviewedAt
          }
        }
      });
    } catch (error) {
      console.error('Reject account error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while rejecting account'
      });
    }
  }
);

// @route   GET /api/admin/users
// @desc    Get all users with filters
// @access  Admin Only
router.get('/users',
  requireRole('admin'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const { role, status, search } = req.query;

      // Build filter object
      const filter = {};
      if (role) filter.role = role;
      if (status) filter.status = status;
      if (search) {
        filter.$or = [
          { email: { $regex: search, $options: 'i' } },
          { 'profile.fullName': { $regex: search, $options: 'i' } },
          { 'profile.companyName': { $regex: search, $options: 'i' } },
          { 'profile.governmentEntity': { $regex: search, $options: 'i' } }
        ];
      }

      const users = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await User.countDocuments(filter);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Users list error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching users'
      });
    }
  }
);

// @route   GET /api/admin/users/:id
// @desc    Get specific user details for admin
// @access  Admin Only
router.get('/users/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id).select('-password');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check if admin is trying to access super admin user data
      if (req.user.role === 'admin' && user.role === 'super_admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Insufficient permissions to view super admin user data.',
          errorCode: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('User details error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user details'
      });
    }
  }
);

// @route   POST /api/admin/suspend-user/:id
// @desc    Suspend a user account
// @access  Admin Only
router.post('/suspend-user/:id',
  [
    body('reason').notEmpty().withMessage('Suspension reason is required')
  ],
  requireRole('admin'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { reason } = req.body;
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (user.role === 'super_admin') {
        return res.status(403).json({
          success: false,
          message: 'Cannot suspend super admin'
        });
      }

      user.status = 'suspended';
      user.rejectionReason = reason;
      await user.save();

      // Send suspension email
      await sendEmail({
        to: user.email,
        subject: 'تم تعليق حسابك - منصة المزادات والمناقصات',
        html: `
          <h2>تعليق الحساب</h2>
          <p>تم تعليق حسابك مؤقتاً للأسباب التالية:</p>
          <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <p style="color: #dc2626; margin: 0;"><strong>سبب التعليق:</strong> ${reason}</p>
          </div>
          <p>للاستفسار عن إعادة تفعيل حسابك، يرجى التواصل مع دعم العملاء.</p>
        `
      });

      res.json({
        success: true,
        message: 'User suspended successfully'
      });
    } catch (error) {
      console.error('Suspend user error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while suspending user'
      });
    }
  }
);

// @route   POST /api/admin/unsuspend-user/:id
// @desc    Unsuspend a user account
// @access  Admin Only
router.post('/unsuspend-user/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (user.status !== 'suspended') {
        return res.status(400).json({
          success: false,
          message: 'User is not suspended'
        });
      }

      user.status = 'approved';
      user.rejectionReason = undefined;
      await user.save();

      // Send reactivation email
      await sendEmail({
        to: user.email,
        subject: 'تم إعادة تفعيل حسابك - منصة المزادات والمناقصات',
        html: `
          <h2>إعادة تفعيل الحساب</h2>
          <p>تم إعادة تفعيل حسابك بنجاح.</p>
          <p>يمكنك الآن تسجيل الدخول والاستفادة من جميع خدمات المنصة.</p>
          <a href="${process.env.CLIENT_URL}/auth/login" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">تسجيل الدخول</a>
        `
      });

      res.json({
        success: true,
        message: 'User unsuspended successfully'
      });
    } catch (error) {
      console.error('Unsuspend user error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while unsuspending user'
      });
    }
  }
);

// @route   GET /api/admin/analytics/auctions
// @desc    Get auction analytics and statistics
// @access  Admin Only
router.get('/analytics/auctions',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      const dateFilter = {};
      
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Basic counts
      const totalAuctions = await Auction.countDocuments(dateFilter);
      const activeAuctions = await Auction.countDocuments({
        ...dateFilter,
        status: 'active'
      });
      const completedAuctions = await Auction.countDocuments({
        ...dateFilter,
        status: 'completed'
      });
      const cancelledAuctions = await Auction.countDocuments({
        ...dateFilter,
        status: 'cancelled'
      });

      // Status distribution
      const statusDistribution = await Auction.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      // Category distribution
      const categoryDistribution = await Auction.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]);

      // Revenue statistics
      const revenueStats = await Auction.aggregate([
        { $match: { ...dateFilter, status: 'completed' } },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$currentBid' },
            averagePrice: { $avg: '$currentBid' },
            maxPrice: { $max: '$currentBid' },
            minPrice: { $min: '$currentBid' }
          }
        }
      ]);

      // Monthly trend (last 12 months)
      const monthlyTrend = await Auction.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(new Date().setMonth(new Date().getMonth() - 11))
            }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 },
            revenue: { $sum: '$currentBid' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      // Top performing auctions
      const topAuctions = await Auction.find({
        ...dateFilter,
        status: 'completed'
      })
        .sort({ currentBid: -1 })
        .limit(10)
        .select('title currentBid bids createdAt category');

      // Bidding activity
      const biddingActivity = await Auction.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            totalBids: { $sum: { $size: '$bids' } },
            averageBidsPerAuction: { $avg: { $size: '$bids' } },
            maxBidsPerAuction: { $max: { $size: '$bids' } }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          overview: {
            total: totalAuctions,
            active: activeAuctions,
            completed: completedAuctions,
            cancelled: cancelledAuctions,
            completionRate: totalAuctions > 0 ? ((completedAuctions / totalAuctions) * 100).toFixed(1) : 0
          },
          statusDistribution,
          categoryDistribution,
          revenue: revenueStats[0] || {
            totalRevenue: 0,
            averagePrice: 0,
            maxPrice: 0,
            minPrice: 0
          },
          monthlyTrend,
          topAuctions,
          biddingActivity: biddingActivity[0] || {
            totalBids: 0,
            averageBidsPerAuction: 0,
            maxBidsPerAuction: 0
          }
        }
      });
    } catch (error) {
      console.error('Auction analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching auction analytics'
      });
    }
  }
);

// @route   GET /api/admin/analytics/tenders
// @desc    Get tender analytics and statistics
// @access  Admin Only
router.get('/analytics/tenders',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      const dateFilter = {};
      
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Basic counts
      const totalTenders = await Tender.countDocuments(dateFilter);
      const activeTenders = await Tender.countDocuments({
        ...dateFilter,
        status: 'active'
      });
      const completedTenders = await Tender.countDocuments({
        ...dateFilter,
        status: 'completed'
      });
      const cancelledTenders = await Tender.countDocuments({
        ...dateFilter,
        status: 'cancelled'
      });

      // Status distribution
      const statusDistribution = await Tender.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      // Category distribution
      const categoryDistribution = await Tender.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]);

      // Government entity distribution
      const governmentDistribution = await Tender.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$governmentEntity', count: { $sum: 1 } } }
      ]);

      // Budget statistics
      const budgetStats = await Tender.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            totalBudget: { $sum: '$budget' },
            averageBudget: { $avg: '$budget' },
            maxBudget: { $max: '$budget' },
            minBudget: { $min: '$budget' }
          }
        }
      ]);

      // Monthly trend (last 12 months)
      const monthlyTrend = await Tender.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(new Date().setMonth(new Date().getMonth() - 11))
            }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 },
            totalBudget: { $sum: '$budget' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      // Top tenders by budget
      const topTenders = await Tender.find(dateFilter)
        .sort({ budget: -1 })
        .limit(10)
        .select('title budget proposals createdAt category');

      // Submission activity
      const submissionActivity = await Tender.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            totalSubmissions: { $sum: { $size: '$proposals' } },
            averageSubmissionsPerTender: { $avg: { $size: '$proposals' } },
            maxSubmissionsPerTender: { $max: { $size: '$proposals' } }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          overview: {
            total: totalTenders,
            active: activeTenders,
            completed: completedTenders,
            cancelled: cancelledTenders,
            completionRate: totalTenders > 0 ? ((completedTenders / totalTenders) * 100).toFixed(1) : 0
          },
          statusDistribution,
          categoryDistribution,
          governmentDistribution,
          budget: budgetStats[0] || {
            totalBudget: 0,
            averageBudget: 0,
            maxBudget: 0,
            minBudget: 0
          },
          monthlyTrend,
          topTenders,
          submissionActivity: submissionActivity[0] || {
            totalSubmissions: 0,
            averageSubmissionsPerTender: 0,
            maxSubmissionsPerTender: 0
          }
        }
      });
    } catch (error) {
      console.error('Tender analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching tender analytics'
      });
    }
  }
);

// @route   GET /api/admin/analytics/users
// @desc    Get user analytics and statistics
// @access  Admin Only
router.get('/analytics/users',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      const dateFilter = {};
      
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Basic counts
      const totalUsers = await User.countDocuments(dateFilter);
      const activeUsers = await User.countDocuments({
        ...dateFilter,
        status: 'approved'
      });
      const pendingUsers = await User.countDocuments({
        ...dateFilter,
        status: { $in: ['documents_submitted', 'under_review'] }
      });
      const suspendedUsers = await User.countDocuments({
        ...dateFilter,
        status: 'suspended'
      });

      // Role distribution
      const roleDistribution = await User.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$role', count: { $sum: 1 } } }
      ]);

      // Status distribution
      const statusDistribution = await User.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      // Monthly registration trend (last 12 months)
      const monthlyRegistrations = await User.aggregate([
        {
          $match: {
            createdAt: {
              $gte: new Date(new Date().setMonth(new Date().getMonth() - 11))
            }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ]);

      // User activity statistics
      const userActivity = await User.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            totalLogins: { $sum: '$loginCount' },
            averageLogins: { $avg: '$loginCount' },
            recentlyActive: {
              $sum: {
                $cond: [
                  {
                    $gte: [
                      '$lastLoginAt',
                      new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      // Top active users (by login count)
      const topActiveUsers = await User.find(dateFilter)
        .sort({ loginCount: -1 })
        .limit(10)
        .select('email role loginCount lastLoginAt createdAt profile.fullName profile.companyName');

      // Geographic distribution - only return real data if available
      // Try to extract city information from user addresses
      const usersWithAddresses = await User.find({
        ...dateFilter,
        $or: [
          { 'profile.address': { $exists: true, $ne: null, $ne: '' } },
          { 'profile.companyAddress': { $exists: true, $ne: null, $ne: '' } }
        ]
      }).select('profile.address profile.companyAddress');

      const geographicDistribution = [];

      if (usersWithAddresses.length > 0) {
        const cityCount = {};

        usersWithAddresses.forEach(user => {
          const address = user.profile?.address || user.profile?.companyAddress || '';
          let city = 'غير محدد';

          // Extract city from address using simple keyword matching
          if (address.includes('الرياض') || address.toLowerCase().includes('riyadh')) {
            city = 'الرياض';
          } else if (address.includes('جدة') || address.toLowerCase().includes('jeddah')) {
            city = 'جدة';
          } else if (address.includes('الدمام') || address.toLowerCase().includes('dammam')) {
            city = 'الدمام';
          } else if (address.includes('مكة') || address.toLowerCase().includes('mecca') || address.toLowerCase().includes('makkah')) {
            city = 'مكة المكرمة';
          } else if (address.includes('المدينة') || address.toLowerCase().includes('medina')) {
            city = 'المدينة المنورة';
          }

          cityCount[city] = (cityCount[city] || 0) + 1;
        });

        // Convert to array format and sort by count
        Object.entries(cityCount).forEach(([city, count]) => {
          if (city !== 'غير محدد') { // Only include identified cities
            geographicDistribution.push({ _id: city, count });
          }
        });

        geographicDistribution.sort((a, b) => b.count - a.count);
      }

      // Verification status
      const verificationStats = await User.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            verified: {
              $sum: {
                $cond: ['$isVerified', 1, 0]
              }
            },
            unverified: {
              $sum: {
                $cond: ['$isVerified', 0, 1]
              }
            }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          overview: {
            total: totalUsers,
            active: activeUsers,
            pending: pendingUsers,
            suspended: suspendedUsers,
            approvalRate: totalUsers > 0 ? ((activeUsers / totalUsers) * 100).toFixed(1) : 0
          },
          roleDistribution,
          statusDistribution,
          monthlyRegistrations,
          activity: userActivity[0] || {
            totalLogins: 0,
            averageLogins: 0,
            recentlyActive: 0
          },
          topActiveUsers,
          geographicDistribution,
          verification: verificationStats[0] || {
            verified: 0,
            unverified: 0
          }
        }
      });
    } catch (error) {
      console.error('User analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user analytics'
      });
    }
  }
);

// @route   GET /api/admin/analytics/overview
// @desc    Get comprehensive platform overview analytics
// @access  Admin Only
router.get('/analytics/overview',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      const dateFilter = {};
      
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Get basic counts for all entities
      const [userCount, auctionCount, tenderCount] = await Promise.all([
        User.countDocuments(dateFilter),
        Auction.countDocuments(dateFilter),
        Tender.countDocuments(dateFilter)
      ]);

      // Get growth rates (compare with previous period)
      const periodLength = endDate && startDate ? 
        new Date(endDate).getTime() - new Date(startDate).getTime() :
        30 * 24 * 60 * 60 * 1000; // 30 days default

      const previousPeriodFilter = {
        createdAt: {
          $gte: new Date(Date.now() - (2 * periodLength)),
          $lt: new Date(Date.now() - periodLength)
        }
      };

      const [prevUserCount, prevAuctionCount, prevTenderCount] = await Promise.all([
        User.countDocuments(previousPeriodFilter),
        Auction.countDocuments(previousPeriodFilter),
        Tender.countDocuments(previousPeriodFilter)
      ]);

      // Calculate growth rates
      const userGrowth = prevUserCount > 0 ? 
        (((userCount - prevUserCount) / prevUserCount) * 100).toFixed(1) : 0;
      const auctionGrowth = prevAuctionCount > 0 ? 
        (((auctionCount - prevAuctionCount) / prevAuctionCount) * 100).toFixed(1) : 0;
      const tenderGrowth = prevTenderCount > 0 ? 
        (((tenderCount - prevTenderCount) / prevTenderCount) * 100).toFixed(1) : 0;

      // Get platform activity summary
      const activitySummary = await Promise.all([
        // Active auctions
        Auction.countDocuments({ status: 'active' }),
        // Active tenders
        Tender.countDocuments({ status: 'active' }),
        // Recent user registrations (last 7 days)
        User.countDocuments({
          createdAt: {
            $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }),
        // Total revenue from completed auctions
        Auction.aggregate([
          { $match: { status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$currentBid' } } }
        ])
      ]);

      res.json({
        success: true,
        data: {
          totals: {
            users: userCount,
            auctions: auctionCount,
            tenders: tenderCount
          },
          growth: {
            users: userGrowth,
            auctions: auctionGrowth,
            tenders: tenderGrowth
          },
          activity: {
            activeAuctions: activitySummary[0],
            activeTenders: activitySummary[1],
            recentRegistrations: activitySummary[2],
            totalRevenue: activitySummary[3][0]?.total || 0
          }
        }
      });
    } catch (error) {
      console.error('Overview analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching overview analytics'
      });
    }
  }
);

// @route   GET /api/admin/reports/financial
// @desc    Get financial reports and statistics
// @access  Admin Only
router.get('/reports/financial',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { period = 'monthly', year = new Date().getFullYear() } = req.query;
      
      // Calculate date range based on period
      const startDate = new Date(year, 0, 1); // Start of year
      const endDate = new Date(year, 11, 31, 23, 59, 59); // End of year
      
      // Get auction financial data
      const auctionStats = await Auction.aggregate([
        {
          $match: {
            status: { $in: ['ended', 'sold'] },
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: null,
            totalAuctions: { $sum: 1 },
            totalRevenue: { $sum: '$currentPrice' },
            averagePrice: { $avg: '$currentPrice' },
            totalBids: { $sum: '$totalBids' }
          }
        }
      ]);
      
      // Get monthly breakdown
      const monthlyData = await Auction.aggregate([
        {
          $match: {
            status: { $in: ['ended', 'sold'] },
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: { $month: '$createdAt' },
            revenue: { $sum: '$currentPrice' },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);
      
      // Get top performing categories
      const categoryStats = await Auction.aggregate([
        {
          $match: {
            status: { $in: ['ended', 'sold'] },
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: '$category',
            revenue: { $sum: '$currentPrice' },
            count: { $sum: 1 },
            averagePrice: { $avg: '$currentPrice' }
          }
        },
        { $sort: { revenue: -1 } },
        { $limit: 10 }
      ]);
      
      // Calculate platform fees (assuming 2.5% fee)
      const platformFeeRate = 0.025;
      const totalRevenue = auctionStats[0]?.totalRevenue || 0;
      const platformFees = totalRevenue * platformFeeRate;
      
      // Get user registration trends
      const userGrowth = await User.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: { $month: '$createdAt' },
            newUsers: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);
      
      res.json({
        success: true,
        data: {
          summary: {
            totalRevenue,
            platformFees,
            totalAuctions: auctionStats[0]?.totalAuctions || 0,
            averageAuctionValue: auctionStats[0]?.averagePrice || 0,
            totalBids: auctionStats[0]?.totalBids || 0
          },
          monthlyTrends: monthlyData.map(item => ({
            month: item._id,
            revenue: item.revenue,
            auctions: item.count
          })),
          topCategories: categoryStats.map(item => ({
            category: item._id,
            revenue: item.revenue,
            count: item.count,
            averagePrice: item.averagePrice
          })),
          userGrowth: userGrowth.map(item => ({
            month: item._id,
            newUsers: item.newUsers
          })),
          period,
          year: parseInt(year)
        }
      });
    } catch (error) {
      console.error('Financial reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating financial reports'
      });
    }
  }
);

// @route   GET /api/admin/reports/overview
// @desc    Get general platform overview reports
// @access  Admin Only
router.get('/reports/overview',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { days = 30 } = req.query;
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      const [userStats, auctionStats, tenderStats] = await Promise.all([
        // User statistics
        User.aggregate([
          {
            $facet: {
              total: [{ $count: 'count' }],
              byRole: [
                { $group: { _id: '$role', count: { $sum: 1 } } }
              ],
              byStatus: [
                { $group: { _id: '$status', count: { $sum: 1 } } }
              ],
              recent: [
                { $match: { createdAt: { $gte: startDate } } },
                { $count: 'count' }
              ]
            }
          }
        ]),
        
        // Auction statistics
        Auction.aggregate([
          {
            $facet: {
              total: [{ $count: 'count' }],
              byStatus: [
                { $group: { _id: '$status', count: { $sum: 1 } } }
              ],
              recent: [
                { $match: { createdAt: { $gte: startDate } } },
                { $count: 'count' }
              ],
              totalValue: [
                {
                  $group: {
                    _id: null,
                    total: { $sum: '$currentPrice' }
                  }
                }
              ]
            }
          }
        ]),
        
        // Tender statistics
        Tender.aggregate([
          {
            $facet: {
              total: [{ $count: 'count' }],
              byStatus: [
                { $group: { _id: '$status', count: { $sum: 1 } } }
              ],
              recent: [
                { $match: { createdAt: { $gte: startDate } } },
                { $count: 'count' }
              ],
              totalBudget: [
                {
                  $group: {
                    _id: null,
                    total: { $sum: '$budget' }
                  }
                }
              ]
            }
          }
        ])
      ]);
      
      res.json({
        success: true,
        data: {
          users: {
            total: userStats[0]?.total[0]?.count || 0,
            recent: userStats[0]?.recent[0]?.count || 0,
            byRole: userStats[0]?.byRole || [],
            byStatus: userStats[0]?.byStatus || []
          },
          auctions: {
            total: auctionStats[0]?.total[0]?.count || 0,
            recent: auctionStats[0]?.recent[0]?.count || 0,
            byStatus: auctionStats[0]?.byStatus || [],
            totalValue: auctionStats[0]?.totalValue[0]?.total || 0
          },
          tenders: {
            total: tenderStats[0]?.total[0]?.count || 0,
            recent: tenderStats[0]?.recent[0]?.count || 0,
            byStatus: tenderStats[0]?.byStatus || [],
            totalBudget: tenderStats[0]?.totalBudget[0]?.total || 0
          },
          period: `${days} days`
        }
      });
    } catch (error) {
      console.error('Overview reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating overview reports'
      });
    }
  }
);

// @route   GET /api/admin/tenders
// @desc    Get all tenders for admin review
// @access  Admin Only
router.get('/tenders',
  requireRole('admin'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const { status, category, search } = req.query;

      // Build filter object
      const filter = {};
      if (status) filter.status = status;
      if (category) filter.category = category;
      if (search) {
        filter.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
          { department: { $regex: search, $options: 'i' } }
        ];
      }

      const tenders = await Tender.find(filter)
        .populate('organizer', 'email profile role')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Tender.countDocuments(filter);

      res.json({
        success: true,
        data: {
          tenders,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Admin tenders list error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching tenders'
      });
    }
  }
);

// @route   GET /api/admin/tenders/:id
// @desc    Get specific tender details for admin
// @access  Admin Only
router.get('/tenders/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const tender = await Tender.findById(req.params.id)
        .populate('organizer', 'email profile role')
        .populate('proposals.proposer', 'email profile role')
        .populate('awardedTo', 'email profile role');

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      res.json({
        success: true,
        data: tender
      });
    } catch (error) {
      console.error('Admin tender details error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching tender details'
      });
    }
  }
);

// @route   GET /api/admin/tenders/:id/submissions
// @desc    Get tender submissions for admin review
// @access  Admin Only
router.get('/tenders/:id/submissions',
  requireRole('admin'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const { status } = req.query;

      const tender = await Tender.findById(req.params.id);
      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      // Filter submissions if status is specified
      let submissions = tender.proposals || [];
      if (status) {
        submissions = submissions.filter(proposal => proposal.status === status);
      }

      // Apply pagination
      const total = submissions.length;
      const paginatedSubmissions = submissions.slice(skip, skip + limit);

      // Populate proposer information
      await Tender.populate(paginatedSubmissions, {
        path: 'proposer',
        select: 'email profile role'
      });

      res.json({
        success: true,
        data: {
          submissions: paginatedSubmissions,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Tender submissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching tender submissions'
      });
    }
  }
);

// @route   PUT /api/admin/tenders/:id/status
// @desc    Update tender status
// @access  Admin Only
router.put('/tenders/:id/status',
  [
    body('status').isIn(['open', 'closed', 'awarded']).withMessage('Invalid status')
  ],
  requireRole('admin'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { status, reason } = req.body;
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      tender.status = status;
      if (reason) {
        tender.adminNotes = reason;
      }
      tender.updatedAt = new Date();

      await tender.save();

      res.json({
        success: true,
        message: `Tender status updated to ${status}`,
        data: {
          id: tender._id,
          status: tender.status
        }
      });
    } catch (error) {
      console.error('Update tender status error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating tender status'
      });
    }
  }
);

// @route   PUT /api/admin/tenders/:tenderId/submissions/:submissionId/status
// @desc    Update tender submission status
// @access  Admin Only
router.put('/tenders/:tenderId/submissions/:submissionId/status',
  [
    body('status').isIn(['pending', 'approved', 'rejected']).withMessage('Invalid status')
  ],
  requireRole('admin'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { status, reason } = req.body;
      const { tenderId, submissionId } = req.params;

      const tender = await Tender.findById(tenderId);
      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      const submission = tender.proposals.id(submissionId);
      if (!submission) {
        return res.status(404).json({
          success: false,
          message: 'Submission not found'
        });
      }

      submission.status = status;
      if (reason) {
        submission.adminNotes = reason;
      }
      submission.reviewedAt = new Date();
      submission.reviewedBy = req.user._id;

      await tender.save();

      res.json({
        success: true,
        message: `Submission status updated to ${status}`,
        data: {
          submissionId: submission._id,
          status: submission.status
        }
      });
    } catch (error) {
      console.error('Update submission status error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating submission status'
      });
    }
  }
);

// @route   POST /api/admin/tenders
// @desc    Create a new tender as admin (on behalf of organization)
// @access  Admin Only
router.post('/tenders',
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('description').notEmpty().withMessage('Description is required'),
    body('category').notEmpty().withMessage('Category is required'),
    body('budget').isNumeric().withMessage('Budget must be a number'),
    body('deadline').isISO8601().withMessage('Valid deadline is required'),
    body('organizerType').isIn(['government', 'company', 'individual']).withMessage('Invalid organizer type'),
    body('organizerInfo').isObject().withMessage('Organizer info is required')
  ],
  requireRole('admin'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        title,
        description,
        category,
        budget,
        startDate,
        deadline,
        location,
        requirements,
        organizerType,
        organizerInfo
      } = req.body;

      // Find or create organizer user based on type
      let organizer;

      if (organizerType === 'government') {
        // Look for existing government user or create one
        organizer = await User.findOne({
          role: 'government',
          'profile.governmentEntity': organizerInfo.governmentEntity
        });

        if (!organizer) {
          // Create a new government user
          organizer = new User({
            email: `${organizerInfo.governmentEntity.toLowerCase().replace(/\s+/g, '')}@gov.sa`,
            password: await bcrypt.hash('temp123456', 12), // Temporary password
            role: 'government',
            status: 'approved',
            emailVerified: true,
            profile: {
              governmentEntity: organizerInfo.governmentEntity,
              fullName: organizerInfo.governmentEntity
            }
          });
          await organizer.save();
        }
      } else if (organizerType === 'company') {
        // Look for existing company user or create one
        organizer = await User.findOne({
          role: 'company',
          'profile.companyName': organizerInfo.companyName
        });

        if (!organizer) {
          // Create a new company user
          organizer = new User({
            email: `${organizerInfo.companyName.toLowerCase().replace(/\s+/g, '')}@company.com`,
            password: await bcrypt.hash('temp123456', 12), // Temporary password
            role: 'company',
            status: 'approved',
            emailVerified: true,
            profile: {
              companyName: organizerInfo.companyName,
              fullName: organizerInfo.companyName
            }
          });
          await organizer.save();
        }
      } else {
        // Individual user
        organizer = await User.findOne({
          role: 'individual',
          'profile.fullName': organizerInfo.fullName
        });

        if (!organizer) {
          // Create a new individual user
          organizer = new User({
            email: `${organizerInfo.fullName.toLowerCase().replace(/\s+/g, '')}@individual.com`,
            password: await bcrypt.hash('temp123456', 12), // Temporary password
            role: 'individual',
            status: 'approved',
            emailVerified: true,
            profile: {
              fullName: organizerInfo.fullName
            }
          });
          await organizer.save();
        }
      }

      // Create the tender
      const tender = new Tender({
        title,
        description,
        category,
        organizer: organizer._id,
        budget,
        startDate: startDate || new Date(),
        deadline: new Date(deadline),
        location: location || {},
        requirements: requirements || [],
        status: 'open',
        createdBy: req.user._id, // Track which admin created it
        adminCreated: true // Flag to indicate admin creation
      });

      await tender.save();

      // Populate organizer info for response
      await tender.populate('organizer', 'email profile role');

      res.status(201).json({
        success: true,
        message: 'Tender created successfully',
        data: tender
      });
    } catch (error) {
      console.error('Admin create tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while creating tender'
      });
    }
  }
);

// @route   GET /api/admin/reports/auctions
// @desc    Get auction reports
// @access  Admin Only
router.get('/reports/auctions',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate, type } = req.query;
      
      // Build date filter
      const dateFilter = {};
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Get auction statistics
      const totalAuctions = await Auction.countDocuments(dateFilter);
      const activeAuctions = await Auction.countDocuments({
        ...dateFilter,
        status: 'active'
      });
      const completedAuctions = await Auction.countDocuments({
        ...dateFilter,
        status: 'completed'
      });
      
      // Calculate total auction value
      const auctionValue = await Auction.aggregate([
        { $match: { ...dateFilter, status: 'completed' } },
        {
          $group: {
            _id: null,
            total: { $sum: '$currentBid' },
            average: { $avg: '$currentBid' }
          }
        }
      ]);

      const totalAuctionValue = auctionValue[0]?.total || 0;
      const averageAuctionValue = auctionValue[0]?.average || 0;

      res.json({
        success: true,
        data: {
          totalAuctions,
          activeAuctions,
          completedAuctions,
          totalAuctionValue,
          averageAuctionValue,
          period: { startDate, endDate },
          type
        }
      });
    } catch (error) {
      console.error('Auction reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating auction reports'
      });
    }
  }
);

// @route   GET /api/admin/reports/tenders
// @desc    Get tender reports
// @access  Admin Only
router.get('/reports/tenders',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate, type } = req.query;
      
      // Build date filter
      const dateFilter = {};
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Get tender statistics
      const totalTenders = await Tender.countDocuments(dateFilter);
      const activeTenders = await Tender.countDocuments({
        ...dateFilter,
        status: 'active'
      });
      const completedTenders = await Tender.countDocuments({
        ...dateFilter,
        status: 'completed'
      });
      
      // Calculate total tender value
      const tenderValue = await Tender.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: null,
            total: { $sum: '$budget' },
            average: { $avg: '$budget' }
          }
        }
      ]);

      const totalTenderValue = tenderValue[0]?.total || 0;
      const averageTenderValue = tenderValue[0]?.average || 0;

      res.json({
        success: true,
        data: {
          totalTenders,
          activeTenders,
          completedTenders,
          totalTenderValue,
          averageTenderValue,
          period: { startDate, endDate },
          type
        }
      });
    } catch (error) {
      console.error('Tender reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating tender reports'
      });
    }
  }
);

// @route   GET /api/admin/reports/users
// @desc    Get user reports
// @access  Admin Only
router.get('/reports/users',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate, type } = req.query;
      
      // Build date filter
      const dateFilter = {};
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Get user statistics
      const totalUsers = await User.countDocuments(dateFilter);
      const activeUsers = await User.countDocuments({
        ...dateFilter,
        status: 'approved'
      });
      const newUsersThisMonth = await User.countDocuments({
        createdAt: {
          $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      });
      const verifiedUsers = await User.countDocuments({
        ...dateFilter,
        isVerified: true
      });
      const pendingApprovals = await User.countDocuments({
        status: { $in: ['documents_submitted', 'under_review'] }
      });

      res.json({
        success: true,
        data: {
          totalUsers,
          activeUsers,
          newUsersThisMonth,
          verifiedUsers,
          pendingApprovals,
          period: { startDate, endDate },
          type
        }
      });
    } catch (error) {
      console.error('User reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating user reports'
      });
    }
  }
);

// @route   GET /api/admin/reports/financial
// @desc    Get financial/payment reports
// @access  Admin Only
router.get('/reports/financial',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { startDate, endDate, type } = req.query;
      
      // Build date filter
      const dateFilter = {};
      if (startDate || endDate) {
        dateFilter.createdAt = {};
        if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
        if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
      }

      // Get payment statistics from completed auctions
      const completedAuctions = await Auction.find({
        ...dateFilter,
        status: 'completed'
      });

      // Calculate payment statistics
      const totalAmount = completedAuctions.reduce((sum, auction) => sum + (auction.currentBid || 0), 0);
      const totalPayments = completedAuctions.length;
      const platformFeeRate = 0.025; // 2.5% platform fee
      const totalPlatformFees = totalAmount * platformFeeRate;
      
      // Mock some payment status data (in a real app, you'd have a payments collection)
      const completedPayments = Math.floor(totalPayments * 0.85);
      const pendingPayments = Math.floor(totalPayments * 0.10);
      const failedPayments = totalPayments - completedPayments - pendingPayments;
      const escrowHeld = totalAmount * 0.15; // Assume 15% is held in escrow

      res.json({
        success: true,
        data: {
          totalAmount,
          totalPlatformFees,
          totalPayments,
          completedPayments,
          pendingPayments,
          failedPayments,
          escrowHeld,
          period: { startDate, endDate },
          type
        }
      });
    } catch (error) {
      console.error('Financial reports error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while generating financial reports'
      });
    }
  }
);

// User activation/deactivation endpoints
router.post('/users/:userId/activate',
  requireRole('admin'),
  async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if admin is trying to activate super admin user
    if (req.user.role === 'admin' && user.role === 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions to modify super admin users.',
        errorCode: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // Update user status
    user.status = 'approved';
    await user.save();

    res.json({
      success: true,
      message: 'User activated successfully',
      data: { user }
    });
  } catch (error) {
    console.error('Error activating user:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

router.post('/users/:userId/deactivate',
  requireRole('admin'),
  async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if admin is trying to deactivate super admin user
    if (req.user.role === 'admin' && user.role === 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Insufficient permissions to modify super admin users.',
        errorCode: 'INSUFFICIENT_PERMISSIONS'
      });
    }

    // Update user status
    user.status = 'suspended';
    await user.save();

    res.json({
      success: true,
      message: 'User deactivated successfully',
      data: { user }
    });
  } catch (error) {
    console.error('Error deactivating user:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// @route   GET /api/admin/auctions
// @desc    Get all auctions for admin management
// @access  Admin Only
router.get('/auctions',
  requireRole('admin'),
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const { status, category, search } = req.query;

      // Build filter object
      const filter = {};
      if (status) filter.status = status;
      if (category) filter.category = category;
      if (search) {
        filter.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const auctions = await Auction.find(filter)
        .populate('seller', 'profile.fullName profile.companyName email')
        .populate('auctioneer', 'profile.fullName profile.companyName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Auction.countDocuments(filter);

      res.json({
        success: true,
        data: {
          auctions,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('Admin auctions fetch error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching auctions'
      });
    }
  }
);

// @route   PUT /api/admin/auctions/:id/approve
// @desc    Approve an auction
// @access  Admin Only
router.put('/auctions/:id/approve',
  requireRole('admin'),
  async (req, res) => {
    try {
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      auction.status = 'active';
      auction.approvedBy = req.user._id;
      auction.approvedAt = new Date();

      await auction.save();

      res.json({
        success: true,
        message: 'Auction approved successfully',
        data: { auction }
      });
    } catch (error) {
      console.error('Auction approval error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while approving auction'
      });
    }
  }
);

// @route   PUT /api/admin/auctions/:id/reject
// @desc    Reject an auction
// @access  Admin Only
router.put('/auctions/:id/reject',
  requireRole('admin'),
  async (req, res) => {
    try {
      const { reason } = req.body;
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      auction.status = 'cancelled';
      auction.rejectionReason = reason;

      await auction.save();

      res.json({
        success: true,
        message: 'Auction rejected successfully',
        data: { auction }
      });
    } catch (error) {
      console.error('Auction rejection error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while rejecting auction'
      });
    }
  }
);

// @route   DELETE /api/admin/auctions/:id
// @desc    Delete an auction (admin only)
// @access  Admin Only
router.delete('/auctions/:id',
  requireRole('admin'),
  async (req, res) => {
    try {
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      // Check if auction has bids
      if (auction.bids && auction.bids.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete auction with existing bids'
        });
      }

      await Auction.findByIdAndDelete(req.params.id);

      res.json({
        success: true,
        message: 'Auction deleted successfully'
      });
    } catch (error) {
      console.error('Auction deletion error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while deleting auction'
      });
    }
  }
);

module.exports = router;
