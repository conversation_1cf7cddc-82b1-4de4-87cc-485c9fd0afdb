const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const ActivityLog = require('../models/ActivityLog');
const { sendEmail } = require('../utils/email');
const { uploadToCloudinary } = require('../utils/cloudinary');
const upload = require('../middleware/upload');
const securityConfig = require('../config/security');

const router = express.Router();

// Generate JWT access token
const generateAccessToken = (userId) => {
  const jwtConfig = securityConfig.getJWTConfig();
  return jwt.sign({ userId }, jwtConfig.secret, {
    expiresIn: jwtConfig.accessTokenExpiry
  });
};

// Generate JWT refresh token
const generateRefreshToken = (userId) => {
  const jwtConfig = securityConfig.getJWTConfig();
  return jwt.sign({
    userId,
    type: 'refresh',
    jti: crypto.randomBytes(16).toString('hex') // Random ID for uniqueness
  }, jwtConfig.refreshSecret, {
    expiresIn: jwtConfig.refreshTokenExpiry
  });
};

// Generate both tokens
const generateTokens = async (user) => {
  const accessToken = generateAccessToken(user._id);
  const refreshToken = generateRefreshToken(user._id);
  
  // Save refresh token to database
  user.refreshToken = refreshToken;
  user.refreshTokenExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  await user.save();
  
  return { accessToken, refreshToken };
};

// @route   POST /api/auth/register
// @desc    Register new user
// @access  Public
router.post('/register', 
  [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }),
    body('role').isIn(['individual', 'company', 'government']),
    body('profile.fullName').notEmpty().trim(),
    body('profile.phone').notEmpty().trim()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, password, role, profile } = req.body;

      // Check if user already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User already exists'
        });
      }

      // Validate role-specific fields
      if (role === 'company' && !profile.companyName) {
        return res.status(400).json({
          success: false,
          message: 'Company name is required for company accounts'
        });
      }

      if (role === 'government' && !profile.governmentEntity) {
        return res.status(400).json({
          success: false,
          message: 'Government entity is required for government accounts'
        });
      }

      // Documents will be uploaded later via separate endpoint
      const documents = [];

      // Create user
      const user = new User({
        email,
        password,
        role,
        profile,
        documents,
        status: 'pending_email_verification'
      });

      // Generate email verification token
      const emailToken = crypto.randomBytes(32).toString('hex');
      user.emailVerificationToken = emailToken;
      user.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

      await user.save();

      // Send verification email
      const verificationUrl = `${process.env.CLIENT_URL}/auth/verify-email?token=${emailToken}`;
      await sendEmail({
        to: user.email,
        subject: 'تأكيد البريد الإلكتروني - منصة المزادات والمناقصات',
        html: `
          <h2>مرحباً ${profile.fullName}</h2>
          <p>شكراً لك على التسجيل في منصة المزادات والمناقصات.</p>
          <p>يرجى النقر على الرابط التالي لتأكيد بريدك الإلكتروني:</p>
          <a href="${verificationUrl}" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">تأكيد البريد الإلكتروني</a>
          <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
        `
      });

      res.status(201).json({
        success: true,
        message: 'User registered successfully. Please check your email for verification.',
        data: {
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
            status: user.status
          }
        }
      });

    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during registration'
      });
    }
  }
);

// @route   GET /api/auth/verify-email
// @desc    Verify email address
// @access  Public
router.get('/verify-email', async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Verification token is required'
      });
    }

    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    user.emailVerified = true;

    // Auto-approve individual accounts if enabled in environment
    if (process.env.AUTO_APPROVE_INDIVIDUAL_ACCOUNTS === 'true' && user.role === 'individual') {
      user.status = 'approved';
      user.reviewedAt = new Date();
      user.reviewedBy = null; // Auto-approved
    } else {
      user.status = 'pending_documents';
    }

    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;

    await user.save();

    res.json({
      success: true,
      message: 'Email verified successfully. Please upload required documents.',
      data: {
        user: {
          id: user._id,
          email: user.email,
          role: user.role,
          status: user.status
        }
      }
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during email verification'
    });
  }
});

// @route   POST /api/auth/resend-verification
// @desc    Resend email verification
// @access  Public
router.post('/resend-verification',
  [body('email').isEmail().normalizeEmail()],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid email address'
        });
      }

      const { email } = req.body;
      const user = await User.findByEmail(email);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (user.emailVerified) {
        return res.status(400).json({
          success: false,
          message: 'Email is already verified'
        });
      }

      // Generate new email verification token
      const emailToken = crypto.randomBytes(32).toString('hex');
      user.emailVerificationToken = emailToken;
      user.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

      await user.save();

      // Send verification email
      const verificationUrl = `${process.env.CLIENT_URL}/auth/verify-email?token=${emailToken}`;
      await sendEmail({
        to: user.email,
        subject: 'تأكيد البريد الإلكتروني - منصة المزادات والمناقصات',
        html: `
          <h2>مرحباً ${user.profile.fullName}</h2>
          <p>شكراً لك على التسجيل في منصة المزادات والمناقصات.</p>
          <p>يرجى النقر على الرابط التالي لتأكيد بريدك الإلكتروني:</p>
          <a href="${verificationUrl}" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">تأكيد البريد الإلكتروني</a>
          <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
        `
      });

      res.json({
        success: true,
        message: 'Verification email sent successfully'
      });

    } catch (error) {
      console.error('Resend verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while resending verification email'
      });
    }
  }
);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login',
  [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty()
  ],
  async (req, res) => {
    try {
      console.log('🔍 Login attempt:', { email: req.body.email, hasPassword: !!req.body.password });

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ Validation errors:', errors.array());
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, password } = req.body;

      // Find user
      console.log('🔍 Looking for user with email:', email);
      const user = await User.findByEmail(email);
      if (!user) {
        console.log('❌ User not found');
        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      console.log('✅ User found:', {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        emailVerified: user.emailVerified,
        isLocked: user.isLocked
      });

      // Check if account is locked
      if (user.isLocked) {
        return res.status(423).json({
          success: false,
          message: 'Account temporarily locked due to too many failed login attempts'
        });
      }

      // Check password
      console.log('🔍 Checking password for user:', user.email);
      const isPasswordValid = await user.comparePassword(password);
      console.log('🔍 Password validation result:', isPasswordValid);
      if (!isPasswordValid) {
        await user.incLoginAttempts();

        // Log failed login attempt
        try {
          await ActivityLog.logActivity({
            userId: user._id,
            action: 'login',
            description: `Failed login attempt for user ${user.email}`,
            category: 'authentication',
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.get('User-Agent'),
            severity: 'warning',
            isSuccessful: false,
            errorMessage: 'Invalid password'
          });
        } catch (logError) {
          console.error('Error logging failed login activity:', logError);
        }

        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check if email is verified
      if (!user.emailVerified) {
        return res.status(403).json({
          success: false,
          message: 'Please verify your email address before logging in'
        });
      }

      // Check account status
      if (user.status === 'blocked' || user.status === 'suspended') {
        return res.status(403).json({
          success: false,
          message: 'Your account has been suspended. Please contact support.'
        });
      }

      // Reset login attempts and update last login
      await user.resetLoginAttempts();
      user.lastLogin = new Date();
      await user.save();

      // Generate tokens
      const { accessToken, refreshToken } = await generateTokens(user);

      // Log successful login activity
      try {
        console.log('🔍 Attempting to log login activity for user:', user.email);
        const logResult = await ActivityLog.logActivity({
          userId: user._id,
          action: 'login',
          description: `User ${user.email} logged in successfully`,
          category: 'authentication',
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.get('User-Agent'),
          severity: 'info',
          isSuccessful: true
        });
        console.log('✅ Login activity logged successfully:', logResult._id);
      } catch (logError) {
        console.error('❌ Error logging login activity:', logError);
      }

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          accessToken,
          refreshToken,
          user: {
            id: user._id,
            email: user.email,
            role: user.role,
            status: user.status,
            profile: user.profile
          }
        }
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during login'
      });
    }
  }
);

// @route   POST /api/auth/forgot-password
// @desc    Send password reset email
// @access  Public
router.post('/forgot-password',
  [body('email').isEmail().normalizeEmail()],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid email address'
        });
      }

      const { email } = req.body;
      const user = await User.findByEmail(email);

      if (!user) {
        return res.json({
          success: true,
          message: 'If an account with that email exists, we have sent a password reset link.'
        });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      user.passwordResetToken = resetToken;
      user.passwordResetExpires = Date.now() + 60 * 60 * 1000; // 1 hour

      await user.save();

      // Send reset email
      const resetUrl = `${process.env.CLIENT_URL}/auth/reset-password?token=${resetToken}`;
      await sendEmail({
        to: user.email,
        subject: 'إعادة تعيين كلمة المرور - منصة المزادات والمناقصات',
        html: `
          <h2>طلب إعادة تعيين كلمة المرور</h2>
          <p>لقد تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك.</p>
          <p>يرجى النقر على الرابط التالي لإعادة تعيين كلمة المرور:</p>
          <a href="${resetUrl}" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">إعادة تعيين كلمة المرور</a>
          <p>هذا الرابط صالح لمدة ساعة واحدة فقط.</p>
          <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
        `
      });

      res.json({
        success: true,
        message: 'If an account with that email exists, we have sent a password reset link.'
      });

    } catch (error) {
      console.error('Forgot password error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during password reset request'
      });
    }
  }
);

// @route   POST /api/auth/reset-password
// @desc    Reset password
// @access  Public
router.post('/reset-password',
  [
    body('token').notEmpty(),
    body('password').isLength({ min: 6 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { token, password } = req.body;

      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpires: { $gt: Date.now() }
      });

      if (!user) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired reset token'
        });
      }

      user.password = password;
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;

      await user.save();

      res.json({
        success: true,
        message: 'Password reset successfully'
      });

    } catch (error) {
      console.error('Reset password error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error during password reset'
      });
    }
  }
);

// @route   POST /api/auth/refresh
// @desc    Refresh access token using refresh token
// @access  Public
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    // Verify refresh token
    let decoded;
    try {
      const jwtConfig = securityConfig.getJWTConfig();
      decoded = jwt.verify(refreshToken, jwtConfig.refreshSecret);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Check if it's a refresh token
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token type'
      });
    }

    // Find user and check if refresh token is still valid
    const user = await User.findById(decoded.userId);
    if (!user || user.refreshToken !== refreshToken || user.refreshTokenExpiry < new Date()) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token expired or invalid'
      });
    }

    // Check if user is still active
    if (user.status === 'blocked' || user.status === 'suspended') {
      return res.status(403).json({
        success: false,
        message: 'Account suspended'
      });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = await generateTokens(user);

    res.json({
      success: true,
      message: 'Tokens refreshed successfully',
      data: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during token refresh'
    });
  }
});

// @route   POST /api/auth/logout
// @desc    Logout user (invalidate refresh token)
// @access  Private
router.post('/logout', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Find user by refresh token and invalidate it
      const user = await User.findOne({ refreshToken });
      if (user) {
        user.refreshToken = null;
        user.refreshTokenExpiry = null;
        await user.save();
      }
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during logout'
    });
  }
});

module.exports = router;
