require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestAdmin() {
  try {
    // Delete existing test admin if exists
    await User.deleteOne({ email: '<EMAIL>' });
    
    // Hash password manually
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    // Create a new admin user with manually hashed password
    const adminUser = new User({
      email: '<EMAIL>',
      password: hashedPassword, // Pre-hashed password
      role: 'admin',
      status: 'approved',
      emailVerified: true,
      profile: {
        fullName: 'Test Admin',
        phone: '+966501234567'
      }
    });

    // Save without triggering pre-save hook for password
    const savedUser = await adminUser.save();
    console.log('✅ Test Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Role:', savedUser.role);
    console.log('✅ Status:', savedUser.status);
    console.log('🎯 Email Verified:', savedUser.emailVerified);
    
    // Test password comparison
    const testUser = await User.findOne({ email: '<EMAIL>' });
    const isValid = await testUser.comparePassword('admin123');
    console.log('🔍 Password test result:', isValid);
    
    console.log('\n🚀 You can now log in with these credentials!');
    
    mongoose.connection.close();
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    mongoose.connection.close();
  }
}

createTestAdmin();
