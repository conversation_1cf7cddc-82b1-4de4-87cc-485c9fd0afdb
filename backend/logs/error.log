{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:16:35:1635","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:16:36:1636","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:36:1636","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:37:1637","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/6870f3660040fd0e29176f8c/approve\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PUT","timestamp":"2025-07-11 14:20:16:2016","url":"/api/admin/users/6870f3660040fd0e29176f8c/approve","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/tenders/687107e023168d085301922c/apply\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-11 15:47:28:4728","url":"/api/tenders/687107e023168d085301922c/apply","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871093b23168d085301924c/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 15:53:15:5315","url":"/api/government/tenders/6871093b23168d085301924c/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710b4f07a04ce831926d6d/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:02:07:27","url":"/api/government/tenders/68710b4f07a04ce831926d6d/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710c1e07a04ce831926ea9/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:05:34:534","url":"/api/government/tenders/68710c1e07a04ce831926ea9/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/68712d53fd8732605b267a80\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:27:36:2736","url":"/api/admin/users/68712d53fd8732605b267a80","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:32:19:3219","url":"/api/admin/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/reports/user-activity\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:35:20:3520","url":"/api/admin/reports/user-activity","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/performance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:36:02:362","url":"/api/admin/performance","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/moderation/flagged\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:37:43:3743","url":"/api/admin/moderation/flagged","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices/2/pay\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:103:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-11 19:05:21:521","url":"/api/billing/invoices/2/pay","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:09:41:941","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:50:050","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:51:051","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:02:02:22","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:53:1653","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:54:1654","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:20:16:2016","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:22:26:2226","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-11 22:56:37:5637","url":"/api/government/applications/68716592746ef9155eefe3a9/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/1/read\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-12 00:02:33:233","url":"/api/government/notifications/1/read","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/mark-all-read\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-12 01:16:26:1626","url":"/api/government/notifications/mark-all-read","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-12 01:19:36:1936","url":"/api/government/notifications/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:35:47:3547","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:35:47:3547","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:08:368","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:08:368","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:10:3610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:10:3610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:21:3621","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:21:3621","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:49:3649","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:49:3649","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:09:379","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:09:379","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:20:3720","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:20:3720","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:41:3741","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:41:3741","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:52:3752","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:52:3752","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 08:38:37:3837","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:38:46:3846","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:38:46:3846","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:47:4247","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:47:4247","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:51:4251","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:51:4251","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:00:430","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:00:430","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:12:4312","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:12:4312","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:20:4320","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:20:4320","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:53:4453","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:53:4453","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:54:4454","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:54:4454","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 08:45:52:4552","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:47:46:4746","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:47:46:4746","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:06:506","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:06:506","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:07:507","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:07:507","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:52:07:527","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:52:07:527","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:56:55:5655","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:56:55:5655","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:46:446","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:46:446","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:51:451","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:51:451","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:55:455","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:55:455","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:07:24:724","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:07:24:724","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:46:1046","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:46:1046","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:50:1050","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:50:1050","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:11:20:1120","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:11:20:1120","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:13:07:137","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:13:07:137","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:14:41:1441","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:14:41:1441","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:17:59:1759","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:17:59:1759","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:19:49:1949","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:19:49:1949","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:26:2226","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:26:2226","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:36:2236","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:36:2236","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:44:2244","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:44:2244","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:54:2254","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:54:2254","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:26:51:2651","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:31:38:3138","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:31:38:3138","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 10:14:17:1417","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:28:38:2838","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:28:38:2838","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:33:13:3313","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:33:13:3313","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:37:51:3751","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:37:51:3751","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:43:19:4319","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:43:19:4319","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:44:19:4419","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:44:19:4419","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:45:15:4515","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:45:15:4515","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:01:11:111","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:01:11:111","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:18:518","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:18:518","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:53:553","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:53:553","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:08:44:844","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:08:44:844","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:05:95","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:05:95","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:22:922","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:22:922","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:39:939","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:39:939","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:52:952","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:52:952","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/bids?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:30:05:305","url":"/api/user/bids?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/bids?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:30:05:305","url":"/api/user/bids?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:40:04:404","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:40:04:404","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:41:17:4117","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:38:4138","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:38:4138","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices/2/pay\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:103:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:41:48:4148","url":"/api/billing/invoices/2/pay","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/deposit\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:42:40:4240","url":"/api/wallet/deposit","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 13:17:45:1745","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 13:17:45:1745","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 13:17:45:1745","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 13:17:45:1745","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/deposit\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 13:18:36:1836","url":"/api/wallet/deposit","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:03:10:310","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:24:15:2415","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/auth/me\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at router.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"GET","timestamp":"2025-07-12 20:26:03:263","url":"/api/auth/me","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:26:03:263","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/auth/login\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at router.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"GET","timestamp":"2025-07-12 20:27:51:2751","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:27:51:2751","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:40:40:4040","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:45:08:458","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 20:52:52:5252","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/company/notifications/mark-all-read\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:37:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","method":"PATCH","timestamp":"2025-07-12 22:43:07:437","url":"/api/company/notifications/mark-all-read","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"687139e729539654fd72b389"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 09:25:31:2531","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:36:47:3647","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:36:47:3647","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:36:47:3647","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:36:47:3647","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/configuration\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:37:06:376","url":"/api/superadmin/configuration","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/configuration\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:37:06:376","url":"/api/superadmin/configuration","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:43:00:430","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:43:00:430","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:43:01:431","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 10:43:01:431","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"PUT","timestamp":"2025-07-13 10:43:17:4317","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/auth/login\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"GET","timestamp":"2025-07-13 11:00:13:013","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 11:00:14:014","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 11:00:50:050","url":"/","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 11:00:50:050","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:44:344","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:44:344","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/settings\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:44:344","url":"/api/superadmin/security/settings","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/security/alerts\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:44:344","url":"/api/superadmin/security/alerts","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/configuration\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:48:348","url":"/api/superadmin/configuration","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /api/superadmin/configuration\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at /Users/<USER>/Desktop/brid1/backend/middleware/auth.js:89:5\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)","method":"GET","timestamp":"2025-07-13 11:03:48:348","url":"/api/superadmin/configuration","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"6870f3660040fd0e29176f88"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 13:57:39:5739","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 13:57:49:5749","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"errorCode":"UNKNOWN_ERROR","ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-13 13:57:59:5759","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
